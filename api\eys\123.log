http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=******************
请求参数: {"name":"\u5f20\u4e09","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=******************
请求参数: {"name":"\u5f20\u4e09","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=******************
请求参数: {"name":"\u5f20\u4e09","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=******************
请求参数: {"name":"\u5f20\u4e09","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=******************
请求参数: {"name":"\u5f20\u4e09","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=******************
请求参数: {"name":"\u5f20\u4e09","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E5%B9%B3&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E5%B9%B3&idcard=******************
请求参数: {"name":"\u6768\u5e73","idcard":"******************"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E5%B9%B3&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E5%B9%B3&idcard=******************
请求参数: {"name":"\u6768\u5e73","idcard":"******************"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E5%B9%B3&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E5%B9%B3&idcard=******************
请求参数: {"name":"\u6768\u5e73","idcard":"******************"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=110101199001011234请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=110101199001011234
请求参数: {"name":"\u5f20\u4e09","idcard":"110101199001011234"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B7%AF%E7%94%9C%E7%94%9C&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B7%AF%E7%94%9C%E7%94%9C&idcard=******************
请求参数: {"name":"\u8def\u751c\u751c","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E5%AD%90%E9%BD%90&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E5%AD%90%E9%BD%90&idcard=******************
请求参数: {"name":"\u674e\u5b50\u9f50","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E5%AD%90%E9%BD%90&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E5%AD%90%E9%BD%90&idcard=******************
请求参数: {"name":"\u674e\u5b50\u9f50","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E9%9D%99&idcard=150702198808082150请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E9%9D%99&idcard=150702198808082150
请求参数: {"name":"\u9648\u9759","idcard":"150702198808082150"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9E%97%E7%82%9C%E7%8E%A5&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9E%97%E7%82%9C%E7%8E%A5&idcard=******************
请求参数: {"name":"\u6797\u709c\u73a5","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%BD%E7%8F%8D&idcard=360733199310070927请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%BD%E7%8F%8D&idcard=360733199310070927
请求参数: {"name":"\u5218\u4e3d\u73cd","idcard":"360733199310070927"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%BD%E7%8F%8D&idcard=360733199310070927请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%BD%E7%8F%8D&idcard=360733199310070927
请求参数: {"name":"\u5218\u4e3d\u73cd","idcard":"360733199310070927"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%BD%E7%8F%8D&idcard=360733199310070927请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%BD%E7%8F%8D&idcard=360733199310070927
请求参数: {"name":"\u5218\u4e3d\u73cd","idcard":"360733199310070927"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9E%97%E7%82%9C%E7%8E%A5&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9E%97%E7%82%9C%E7%8E%A5&idcard=******************
请求参数: {"name":"\u6797\u709c\u73a5","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%BD%E7%8F%8D&idcard=360733199310070920请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%BD%E7%8F%8D&idcard=360733199310070920
请求参数: {"name":"\u5218\u4e3d\u73cd","idcard":"360733199310070920"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%BD%E7%8F%8D&idcard=360733199310070927请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%BD%E7%8F%8D&idcard=360733199310070927
请求参数: {"name":"\u5218\u4e3d\u73cd","idcard":"360733199310070927"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E6%83%A0%E5%B9%B3&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E6%83%A0%E5%B9%B3&idcard=******************
请求参数: {"name":"\u5f20\u60e0\u5e73","idcard":"******************"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9E%97%E7%82%9C%E7%8E%A5&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9E%97%E7%82%9C%E7%8E%A5&idcard=******************
请求参数: {"name":"\u6797\u709c\u73a5","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%BD%E7%8F%8D&idcard=360733199310070927请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%BD%E7%8F%8D&idcard=360733199310070927
请求参数: {"name":"\u5218\u4e3d\u73cd","idcard":"360733199310070927"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9E%97%E7%82%9C%E7%8E%A5&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9E%97%E7%82%9C%E7%8E%A5&idcard=******************
请求参数: {"name":"\u6797\u709c\u73a5","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%BD%E7%8F%8D&idcard=360733199310070925请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%BD%E7%8F%8D&idcard=360733199310070925
请求参数: {"name":"\u5218\u4e3d\u73cd","idcard":"360733199310070925"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E5%8D%9A%E5%AE%8F&idcard=350128201109050013请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E5%8D%9A%E5%AE%8F&idcard=350128201109050013
请求参数: {"name":"\u9648\u535a\u5b8f","idcard":"350128201109050013"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%94%90%E7%AB%8B%E7%BA%A2&idcard=341126199106232520请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%94%90%E7%AB%8B%E7%BA%A2&idcard=341126199106232520
请求参数: {"name":"\u5510\u7acb\u7ea2","idcard":"341126199106232520"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E6%A2%93%E5%87%AF&idcard=510681201101173018请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E6%A2%93%E5%87%AF&idcard=510681201101173018
请求参数: {"name":"\u5218\u6893\u51ef","idcard":"510681201101173018"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%BB%84%E6%B0%B4%E4%BB%99&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%BB%84%E6%B0%B4%E4%BB%99&idcard=******************
请求参数: {"name":"\u9ec4\u6c34\u4ed9","idcard":"******************"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B5%E9%95%BF%E7%94%9F&idcard=360602195301181512请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B5%E9%95%BF%E7%94%9F&idcard=360602195301181512
请求参数: {"name":"\u90b5\u957f\u751f","idcard":"360602195301181512"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%8E%8B%E8%87%AA%E9%BE%99&idcard=360602195301181512请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%8E%8B%E8%87%AA%E9%BE%99&idcard=360602195301181512
请求参数: {"name":"\u738b\u81ea\u9f99","idcard":"360602195301181512"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%9F%A6%E6%AC%A3%E4%BB%81&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%9F%A6%E6%AC%A3%E4%BB%81&idcard=******************
请求参数: {"name":"\u97e6\u6b23\u4ec1","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%AB%98%E5%B2%A9&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%AB%98%E5%B2%A9&idcard=******************
请求参数: {"name":"\u9ad8\u5ca9","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%86%E8%AF%97%E9%9F%B5&idcard=310106200505201629请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%86%E8%AF%97%E9%9F%B5&idcard=310106200505201629
请求参数: {"name":"\u9646\u8bd7\u97f5","idcard":"310106200505201629"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%91%A8%E6%A6%86%E5%87%AF&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%91%A8%E6%A6%86%E5%87%AF&idcard=******************
请求参数: {"name":"\u5468\u6986\u51ef","idcard":"******************"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E5%AE%9A%E7%83%A8&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E5%AE%9A%E7%83%A8&idcard=******************
请求参数: {"name":"\u6768\u5b9a\u70e8","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%AB%98%E5%AD%90%E6%B7%87&idcard=320321201102111427请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%AB%98%E5%AD%90%E6%B7%87&idcard=320321201102111427
请求参数: {"name":"\u9ad8\u5b50\u6dc7","idcard":"320321201102111427"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%AB%98%E5%AD%90%E6%B7%87&idcard=320321201102111427请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%AB%98%E5%AD%90%E6%B7%87&idcard=320321201102111427
请求参数: {"name":"\u9ad8\u5b50\u6dc7","idcard":"320321201102111427"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E8%89%BA%E6%B8%B2&idcard=36062220120206391X请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E8%89%BA%E6%B8%B2&idcard=36062220120206391X
请求参数: {"name":"\u5f20\u827a\u6e32","idcard":"36062220120206391X"}
响应数据: 

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%94%A1%E5%90%91%E9%AB%98&idcard=620702201210237813请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%94%A1%E5%90%91%E9%AB%98&idcard=620702201210237813
请求参数: {"name":"\u8521\u5411\u9ad8","idcard":"620702201210237813"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%91%A8%E6%9F%AF%E5%AE%87&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%91%A8%E6%9F%AF%E5%AE%87&idcard=******************
请求参数: {"name":"\u5468\u67ef\u5b87","idcard":"******************"}
响应数据: 

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%91%A8%E6%9F%AF%E5%AE%87&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%91%A8%E6%9F%AF%E5%AE%87&idcard=******************
请求参数: {"name":"\u5468\u67ef\u5b87","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E4%BD%B3%E6%80%A1&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E4%BD%B3%E6%80%A1&idcard=******************
请求参数: {"name":"\u674e\u4f73\u6021","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E8%B7%83%E8%99%8E&idcard=211322197711125317请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E8%B7%83%E8%99%8E&idcard=211322197711125317
请求参数: {"name":"\u5f20\u8dc3\u864e","idcard":"211322197711125317"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%AE%87%E7%BA%AF&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%AE%87%E7%BA%AF&idcard=******************
请求参数: {"name":"\u5218\u5b87\u7eaf","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E5%A4%A7%E6%A0%B9&idcard=130427197908254717请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E5%A4%A7%E6%A0%B9&idcard=130427197908254717
请求参数: {"name":"\u6768\u5927\u6839","idcard":"130427197908254717"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%96%BD%E5%A5%87&idcard=130227198306108804请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%96%BD%E5%A5%87&idcard=130227198306108804
请求参数: {"name":"\u65bd\u5947","idcard":"130227198306108804"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%96%BD%E5%A5%87&idcard=130227198306108804请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%96%BD%E5%A5%87&idcard=130227198306108804
请求参数: {"name":"\u65bd\u5947","idcard":"130227198306108804"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%96%BD%E5%A5%87&idcard=130227198306108804请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%96%BD%E5%A5%87&idcard=130227198306108804
请求参数: {"name":"\u65bd\u5947","idcard":"130227198306108804"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9B%B2%E5%86%A0&idcard=130284196102044181请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9B%B2%E5%86%A0&idcard=130284196102044181
请求参数: {"name":"\u66f2\u51a0","idcard":"130284196102044181"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9B%B2%E5%86%A0&idcard=130284196102044181请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9B%B2%E5%86%A0&idcard=130284196102044181
请求参数: {"name":"\u66f2\u51a0","idcard":"130284196102044181"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E5%8F%8B%E8%89%BA&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E5%8F%8B%E8%89%BA&idcard=******************
请求参数: {"name":"\u5f20\u53cb\u827a","idcard":"******************"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%82%96%E8%8A%B8&idcard=511322200810274882请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%82%96%E8%8A%B8&idcard=511322200810274882
请求参数: {"name":"\u8096\u82b8","idcard":"511322200810274882"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%91%A3%E5%BC%88%E5%8D%9A&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%91%A3%E5%BC%88%E5%8D%9A&idcard=******************
请求参数: {"name":"\u8463\u5f08\u535a","idcard":"******************"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E6%9F%8F%E5%AE%87&idcard=330226200510247036请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E6%9F%8F%E5%AE%87&idcard=330226200510247036
请求参数: {"name":"\u9648\u67cf\u5b87","idcard":"330226200510247036"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979
请求参数: {"name":"\u6b66\u660e\u6d9b","idcard":"320981200805042979"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979
请求参数: {"name":"\u6b66\u660e\u6d9b","idcard":"320981200805042979"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979
请求参数: {"name":"\u6b66\u660e\u6d9b","idcard":"320981200805042979"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%BB%B6%E8%8A%B3&idcard=620122197809152318请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%BB%B6%E8%8A%B3&idcard=620122197809152318
请求参数: {"name":"\u5218\u5ef6\u82b3","idcard":"620122197809152318"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E6%B7%91%E7%AB%AF&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E6%B7%91%E7%AB%AF&idcard=******************
请求参数: {"name":"\u674e\u6dd1\u7aef","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E6%B7%91%E7%AB%AF&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E6%B7%91%E7%AB%AF&idcard=******************
请求参数: {"name":"\u674e\u6dd1\u7aef","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E6%B7%91%E7%AB%AF&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E6%B7%91%E7%AB%AF&idcard=******************
请求参数: {"name":"\u674e\u6dd1\u7aef","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=320981200804052718请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=320981200804052718
请求参数: {"name":"\u5f20\u4e09","idcard":"320981200804052718"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979
请求参数: {"name":"\u6b66\u660e\u6d9b","idcard":"320981200805042979"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979
请求参数: {"name":"\u6b66\u660e\u6d9b","idcard":"320981200805042979"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979
请求参数: {"name":"\u6b66\u660e\u6d9b","idcard":"320981200805042979"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979
请求参数: {"name":"\u6b66\u660e\u6d9b","idcard":"320981200805042979"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979
请求参数: {"name":"\u6b66\u660e\u6d9b","idcard":"320981200805042979"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979
请求参数: {"name":"\u6b66\u660e\u6d9b","idcard":"320981200805042979"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%BD%98%E5%8D%8E%E4%B8%BD&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%BD%98%E5%8D%8E%E4%B8%BD&idcard=******************
请求参数: {"name":"\u6f58\u534e\u4e3d","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042972请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042972
请求参数: {"name":"\u6b66\u660e\u6d9b","idcard":"320981200805042972"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%B8%A5%E7%B4%A0%E8%BF%90&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%B8%A5%E7%B4%A0%E8%BF%90&idcard=******************
请求参数: {"name":"\u4e25\u7d20\u8fd0","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=320981200804052718请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=320981200804052718
请求参数: {"name":"\u5f20\u4e09","idcard":"320981200804052718"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=320981200804052718请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=320981200804052718
请求参数: {"name":"\u5f20\u4e09","idcard":"320981200804052718"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%91%A8%E5%8D%8E%E4%B8%BD&idcard=33900519910812672X请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%91%A8%E5%8D%8E%E4%B8%BD&idcard=33900519910812672X
请求参数: {"name":"\u5468\u534e\u4e3d","idcard":"33900519910812672X"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%BB%84%E7%8E%89%E5%B3%B0&idcard=341224200402054317请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%BB%84%E7%8E%89%E5%B3%B0&idcard=341224200402054317
请求参数: {"name":"\u9ec4\u7389\u5cf0","idcard":"341224200402054317"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E6%99%9F%E6%B4%A5&idcard=330302200508307639请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E6%99%9F%E6%B4%A5&idcard=330302200508307639
请求参数: {"name":"\u5f90\u665f\u6d25","idcard":"330302200508307639"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E6%99%9F%E6%B4%A5&idcard=330302200508307639请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E6%99%9F%E6%B4%A5&idcard=330302200508307639
请求参数: {"name":"\u5f90\u665f\u6d25","idcard":"330302200508307639"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%8E%8B%E5%85%89%E8%8A%B9&idcard=533022200311162228请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%8E%8B%E5%85%89%E8%8A%B9&idcard=533022200311162228
请求参数: {"name":"\u738b\u5149\u82b9","idcard":"533022200311162228"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%91%A3%E9%AA%8F%E9%91%AB&idcard=342401200204038479请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%91%A3%E9%AA%8F%E9%91%AB&idcard=342401200204038479
请求参数: {"name":"\u8463\u9a8f\u946b","idcard":"342401200204038479"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%BD%95%E5%9B%BD%E4%B8%AD&idcard=51112919860205423X请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%BD%95%E5%9B%BD%E4%B8%AD&idcard=51112919860205423X
请求参数: {"name":"\u4f55\u56fd\u4e2d","idcard":"51112919860205423X"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B7%AF%E7%94%9C%E7%94%9C&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B7%AF%E7%94%9C%E7%94%9C&idcard=******************
请求参数: {"name":"\u8def\u751c\u751c","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B7%AF%E7%94%9C%E7%94%9C&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B7%AF%E7%94%9C%E7%94%9C&idcard=******************
请求参数: {"name":"\u8def\u751c\u751c","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%BB%98%E5%A4%A9%E4%BF%8A&idcard=320381200906080090请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%BB%98%E5%A4%A9%E4%BF%8A&idcard=320381200906080090
请求参数: {"name":"\u4ed8\u5929\u4fca","idcard":"320381200906080090"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%A2%81%E4%BA%91%E9%AA%A5&idcard=44098119990627325X请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%A2%81%E4%BA%91%E9%AA%A5&idcard=44098119990627325X
请求参数: {"name":"\u6881\u4e91\u9aa5","idcard":"44098119990627325X"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9E%97%E8%BF%9B%E5%96%9C&idcard=350623200310236312请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9E%97%E8%BF%9B%E5%96%9C&idcard=350623200310236312
请求参数: {"name":"\u6797\u8fdb\u559c","idcard":"350623200310236312"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%B6%E4%B9%A6%E5%9C%86&idcard=321282200502022427请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%B6%E4%B9%A6%E5%9C%86&idcard=321282200502022427
请求参数: {"name":"\u9676\u4e66\u5706","idcard":"321282200502022427"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%83%AD%E9%A1%BA%E9%94%8B&idcard=350583200305227412请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%83%AD%E9%A1%BA%E9%94%8B&idcard=350583200305227412
请求参数: {"name":"\u90ed\u987a\u950b","idcard":"350583200305227412"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%83%AD%E9%A1%BA%E9%94%8B&idcard=350583200305227412请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%83%AD%E9%A1%BA%E9%94%8B&idcard=350583200305227412
请求参数: {"name":"\u90ed\u987a\u950b","idcard":"350583200305227412"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%AD%90%E6%B6%B5&idcard=330303202308249611请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%AD%90%E6%B6%B5&idcard=330303202308249611
请求参数: {"name":"\u5218\u5b50\u6db5","idcard":"330303202308249611"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%AD%90%E6%B6%B5&idcard=330303202308249618请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%AD%90%E6%B6%B5&idcard=330303202308249618
请求参数: {"name":"\u5218\u5b50\u6db5","idcard":"330303202308249618"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%AD%90%E6%B6%B5&idcard=330303202308249613请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%AD%90%E6%B6%B5&idcard=330303202308249613
请求参数: {"name":"\u5218\u5b50\u6db5","idcard":"330303202308249613"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%AD%90%E6%B6%B5&idcard=330303202308249615请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%AD%90%E6%B6%B5&idcard=330303202308249615
请求参数: {"name":"\u5218\u5b50\u6db5","idcard":"330303202308249615"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%AD%90%E6%B6%B5&idcard=330303202308249619请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%AD%90%E6%B6%B5&idcard=330303202308249619
请求参数: {"name":"\u5218\u5b50\u6db5","idcard":"330303202308249619"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%AD%90%E6%B6%B5&idcard=330303202308249610请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%AD%90%E6%B6%B5&idcard=330303202308249610
请求参数: {"name":"\u5218\u5b50\u6db5","idcard":"330303202308249610"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%AD%90%E6%B6%B5&idcard=330303202308249612请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%AD%90%E6%B6%B5&idcard=330303202308249612
请求参数: {"name":"\u5218\u5b50\u6db5","idcard":"330303202308249612"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%AD%90%E6%B6%B5&idcard=330303202308249614请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%AD%90%E6%B6%B5&idcard=330303202308249614
请求参数: {"name":"\u5218\u5b50\u6db5","idcard":"330303202308249614"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%AD%90%E6%B6%B5&idcard=330303202308249617请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%AD%90%E6%B6%B5&idcard=330303202308249617
请求参数: {"name":"\u5218\u5b50\u6db5","idcard":"330303202308249617"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%AD%90%E6%B6%B5&idcard=330303202308249616请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%AD%90%E6%B6%B5&idcard=330303202308249616
请求参数: {"name":"\u5218\u5b50\u6db5","idcard":"330303202308249616"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9E%97%E4%BD%B3%E4%BD%B3&idcard=532627201212261528请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9E%97%E4%BD%B3%E4%BD%B3&idcard=532627201212261528
请求参数: {"name":"\u6797\u4f73\u4f73","idcard":"532627201212261528"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E4%BF%8A%E8%B1%AA&idcard=331121201011135695请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E4%BF%8A%E8%B1%AA&idcard=331121201011135695
请求参数: {"name":"\u9648\u4fca\u8c6a","idcard":"331121201011135695"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E4%BF%8A%E8%B1%AA&idcard=331121201011135695请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E4%BF%8A%E8%B1%AA&idcard=331121201011135695
请求参数: {"name":"\u9648\u4fca\u8c6a","idcard":"331121201011135695"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E4%BF%8A%E8%B1%AA&idcard=331121201011135695请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E4%BF%8A%E8%B1%AA&idcard=331121201011135695
请求参数: {"name":"\u9648\u4fca\u8c6a","idcard":"331121201011135695"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9F%AF%E6%A5%9A%E6%9E%97&idcard=360481200806152221请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9F%AF%E6%A5%9A%E6%9E%97&idcard=360481200806152221
请求参数: {"name":"\u67ef\u695a\u6797","idcard":"360481200806152221"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9F%AF%E6%A5%9A%E6%9E%97&idcard=360481200806152221请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9F%AF%E6%A5%9A%E6%9E%97&idcard=360481200806152221
请求参数: {"name":"\u67ef\u695a\u6797","idcard":"360481200806152221"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%AE%B8%E6%A2%A6%E5%A9%B7&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%AE%B8%E6%A2%A6%E5%A9%B7&idcard=******************
请求参数: {"name":"\u8bb8\u68a6\u5a77","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%AE%B8%E6%A2%A6&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%AE%B8%E6%A2%A6&idcard=******************
请求参数: {"name":"\u8bb8\u68a6","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%AE%B8%E6%A2%A6%E5%A9%B7&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%AE%B8%E6%A2%A6%E5%A9%B7&idcard=******************
请求参数: {"name":"\u8bb8\u68a6\u5a77","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E8%8A%B9&idcard=321321198606112745请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E8%8A%B9&idcard=321321198606112745
请求参数: {"name":"\u674e\u82b9","idcard":"321321198606112745"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E7%88%BD%E7%88%BD&idcard=330302199812197328请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E7%88%BD%E7%88%BD&idcard=330302199812197328
请求参数: {"name":"\u5f90\u723d\u723d","idcard":"330302199812197328"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E7%88%BD%E7%88%BD&idcard=330302199211072829请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E7%88%BD%E7%88%BD&idcard=330302199211072829
请求参数: {"name":"\u5f90\u723d\u723d","idcard":"330302199211072829"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%AD%99%E9%9D%96%E5%96%9C&idcard=370321200603011226请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%AD%99%E9%9D%96%E5%96%9C&idcard=370321200603011226
请求参数: {"name":"\u5b59\u9756\u559c","idcard":"370321200603011226"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%A2%81%E6%B5%A9%E6%99%B4&idcard=370830199909261226请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%A2%81%E6%B5%A9%E6%99%B4&idcard=370830199909261226
请求参数: {"name":"\u8881\u6d69\u6674","idcard":"370830199909261226"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%8E%8B%E9%B9%8F%E5%AE%87&idcard=320115201001150314请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%8E%8B%E9%B9%8F%E5%AE%87&idcard=320115201001150314
请求参数: {"name":"\u738b\u9e4f\u5b87","idcard":"320115201001150314"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%8E%8B%E9%B9%8F%E5%AE%87&idcard=320115201001150314请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%8E%8B%E9%B9%8F%E5%AE%87&idcard=320115201001150314
请求参数: {"name":"\u738b\u9e4f\u5b87","idcard":"320115201001150314"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%BB%84%E7%9D%BF%E6%B4%8B&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%BB%84%E7%9D%BF%E6%B4%8B&idcard=******************
请求参数: {"name":"\u9ec4\u777f\u6d0b","idcard":"******************"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%B7%A6%E9%9B%85&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%B7%A6%E9%9B%85&idcard=******************
请求参数: {"name":"\u5de6\u96c5","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%A7%9A%E4%BA%91%E5%A4%A9&idcard=320202200902025018请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%A7%9A%E4%BA%91%E5%A4%A9&idcard=320202200902025018
请求参数: {"name":"\u59da\u4e91\u5929","idcard":"320202200902025018"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%B2%99%E9%A3%9E%E5%BC%BA&idcard=320682200906060092请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%B2%99%E9%A3%9E%E5%BC%BA&idcard=320682200906060092
请求参数: {"name":"\u6c99\u98de\u5f3a","idcard":"320682200906060092"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%AD%99%E9%80%B8%E8%8F%B2&idcard=330621201302020843请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%AD%99%E9%80%B8%E8%8F%B2&idcard=330621201302020843
请求参数: {"name":"\u5b59\u9038\u83f2","idcard":"330621201302020843"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9C%B1%E6%AD%A6%E5%A8%81&idcard=330681199209031034请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9C%B1%E6%AD%A6%E5%A8%81&idcard=330681199209031034
请求参数: {"name":"\u6731\u6b66\u5a01","idcard":"330681199209031034"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E4%B8%9C%E9%B9%8F&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E4%B8%9C%E9%B9%8F&idcard=******************
请求参数: {"name":"\u9648\u4e1c\u9e4f","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E5%98%89%E5%A9%B5&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E5%98%89%E5%A9%B5&idcard=******************
请求参数: {"name":"\u674e\u5609\u5a75","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%A9%AC%E4%BF%8A%E7%86%99&idcard=511321201006077018请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%A9%AC%E4%BF%8A%E7%86%99&idcard=511321201006077018
请求参数: {"name":"\u9a6c\u4fca\u7199","idcard":"511321201006077018"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%8E%8B%E6%99%93%E5%9F%B9&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%8E%8B%E6%99%93%E5%9F%B9&idcard=******************
请求参数: {"name":"\u738b\u6653\u57f9","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%96%9B%E9%92%A2&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%96%9B%E9%92%A2&idcard=******************
请求参数: {"name":"\u859b\u94a2","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9B%B9%E6%A2%93%E4%BD%9F&idcard=130131201103083612请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9B%B9%E6%A2%93%E4%BD%9F&idcard=130131201103083612
请求参数: {"name":"\u66f9\u6893\u4f5f","idcard":"130131201103083612"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9B%B9%E6%A2%93%E4%BD%9F&idcard=130131201103083612请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9B%B9%E6%A2%93%E4%BD%9F&idcard=130131201103083612
请求参数: {"name":"\u66f9\u6893\u4f5f","idcard":"130131201103083612"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%BD%98%E6%AD%A3%E9%98%B3&idcard=510105199507192274请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%BD%98%E6%AD%A3%E9%98%B3&idcard=510105199507192274
请求参数: {"name":"\u6f58\u6b63\u9633","idcard":"510105199507192274"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%AE%81%E7%81%AB%E7%88%B1&idcard=360202198501052365请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%AE%81%E7%81%AB%E7%88%B1&idcard=360202198501052365
请求参数: {"name":"\u5b81\u706b\u7231","idcard":"360202198501052365"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E5%AE%87%E6%B5%A9&idcard=21078120090716281X请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E5%AE%87%E6%B5%A9&idcard=21078120090716281X
请求参数: {"name":"\u674e\u5b87\u6d69","idcard":"21078120090716281X"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E5%AE%87%E6%B5%A9&idcard=211022199606050539请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E5%AE%87%E6%B5%A9&idcard=211022199606050539
请求参数: {"name":"\u674e\u5b87\u6d69","idcard":"211022199606050539"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%83%91%E5%A9%B7&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%83%91%E5%A9%B7&idcard=******************
请求参数: {"name":"\u90d1\u5a77","idcard":"******************"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%BD%97%E5%85%B0&idcard=532730199309261528请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%BD%97%E5%85%B0&idcard=532730199309261528
请求参数: {"name":"\u7f57\u5170","idcard":"532730199309261528"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%B0%84%E6%9E%97%E4%B8%BD&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%B0%84%E6%9E%97%E4%B8%BD&idcard=******************
请求参数: {"name":"\u5c04\u6797\u4e3d","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BB%96%E5%85%83%E4%B8%B0&idcard=370830199302010013请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BB%96%E5%85%83%E4%B8%B0&idcard=370830199302010013
请求参数: {"name":"\u5ed6\u5143\u4e30","idcard":"370830199302010013"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B5%B5%E8%8D%A3%E6%B5%A9&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B5%B5%E8%8D%A3%E6%B5%A9&idcard=******************
请求参数: {"name":"\u8d75\u8363\u6d69","idcard":"******************"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%BA%93%E6%96%B0%E5%AA%9B&idcard=371202200808032641请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%BA%93%E6%96%B0%E5%AA%9B&idcard=371202200808032641
请求参数: {"name":"\u4e93\u65b0\u5a9b","idcard":"371202200808032641"}
响应数据: 

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%89%BE%E5%85%89%E7%BA%A2&idcard=37132319820306144X请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%89%BE%E5%85%89%E7%BA%A2&idcard=37132319820306144X
请求参数: {"name":"\u827e\u5149\u7ea2","idcard":"37132319820306144X"}
响应数据: {"message":"sso身份认证失败","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%9F%A6%E4%B8%BD%E5%A8%9C&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%9F%A6%E4%B8%BD%E5%A8%9C&idcard=******************
请求参数: {"name":"\u97e6\u4e3d\u5a1c","idcard":"******************"}
响应数据: {"message":"sso身份认证失败","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%9F%A6%E4%B8%BD%E5%A8%9C&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%9F%A6%E4%B8%BD%E5%A8%9C&idcard=******************
请求参数: {"name":"\u97e6\u4e3d\u5a1c","idcard":"******************"}
响应数据: {"message":"sso身份认证失败","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9B%B9%E6%A2%93%E4%BD%9F&idcard=130131201103083612请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9B%B9%E6%A2%93%E4%BD%9F&idcard=130131201103083612
请求参数: {"name":"\u66f9\u6893\u4f5f","idcard":"130131201103083612"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%8E%8B%E4%B8%96%E5%85%B0&idcard=500114200310176154请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%8E%8B%E4%B8%96%E5%85%B0&idcard=500114200310176154
请求参数: {"name":"\u738b\u4e16\u5170","idcard":"500114200310176154"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E7%A5%81%E6%98%8E&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E7%A5%81%E6%98%8E&idcard=******************
请求参数: {"name":"\u674e\u7941\u660e","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%9F%A9%E6%99%93%E9%A3%8E&idcard=220822200507170412请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%9F%A9%E6%99%93%E9%A3%8E&idcard=220822200507170412
请求参数: {"name":"\u97e9\u6653\u98ce","idcard":"220822200507170412"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E6%80%9D%E5%BD%A4&idcard=321321201010015922请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E6%80%9D%E5%BD%A4&idcard=321321201010015922
请求参数: {"name":"\u5f90\u601d\u5f64","idcard":"321321201010015922"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%83%91%E5%8F%8B%E5%BB%BA&idcard=140932201108238427请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%83%91%E5%8F%8B%E5%BB%BA&idcard=140932201108238427
请求参数: {"name":"\u90d1\u53cb\u5efa","idcard":"140932201108238427"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%8E%8B%E5%B0%8F%E5%AE%BE&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%8E%8B%E5%B0%8F%E5%AE%BE&idcard=******************
请求参数: {"name":"\u738b\u5c0f\u5bbe","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%BB%84%E5%9B%BD%E5%BF%A0&idcard=510702196608245814请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%BB%84%E5%9B%BD%E5%BF%A0&idcard=510702196608245814
请求参数: {"name":"\u9ec4\u56fd\u5fe0","idcard":"510702196608245814"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B4%B9%E5%9B%BD%E5%BF%A0&idcard=510702196608245814请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B4%B9%E5%9B%BD%E5%BF%A0&idcard=510702196608245814
请求参数: {"name":"\u8d39\u56fd\u5fe0","idcard":"510702196608245814"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%BB%8E%E6%97%8F%E5%85%89&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%BB%8E%E6%97%8F%E5%85%89&idcard=******************
请求参数: {"name":"\u9ece\u65cf\u5149","idcard":"******************"}
响应数据: 

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E8%8E%B9%E8%8E%B9&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E8%8E%B9%E8%8E%B9&idcard=******************
请求参数: {"name":"\u5f20\u83b9\u83b9","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BD%AD%E6%98%8E%E6%99%BA&idcard=362526197104110025请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BD%AD%E6%98%8E%E6%99%BA&idcard=362526197104110025
请求参数: {"name":"\u5f6d\u660e\u667a","idcard":"362526197104110025"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%B6%E6%98%8E%E5%AF%8C&idcard=532627200010211536请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%B6%E6%98%8E%E5%AF%8C&idcard=532627200010211536
请求参数: {"name":"\u9676\u660e\u5bcc","idcard":"532627200010211536"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E6%B5%B7%E6%B6%9B&idcard=522123200209152030请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E6%B5%B7%E6%B6%9B&idcard=522123200209152030
请求参数: {"name":"\u6768\u6d77\u6d9b","idcard":"522123200209152030"}
响应数据: 

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%9F%A9%E6%99%93%E9%A3%8E&idcard=220822200507170412请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%9F%A9%E6%99%93%E9%A3%8E&idcard=220822200507170412
请求参数: {"name":"\u97e9\u6653\u98ce","idcard":"220822200507170412"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E6%80%9D%E5%BD%A4&idcard=321321201010015922请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E6%80%9D%E5%BD%A4&idcard=321321201010015922
请求参数: {"name":"\u5f90\u601d\u5f64","idcard":"321321201010015922"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%88%B4%E5%BF%97%E8%B4%B5&idcard=320981198810032978请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%88%B4%E5%BF%97%E8%B4%B5&idcard=320981198810032978
请求参数: {"name":"\u6234\u5fd7\u8d35","idcard":"320981198810032978"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%88%B4%E5%BF%97%E8%B4%B5&idcard=320981198810032978请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%88%B4%E5%BF%97%E8%B4%B5&idcard=320981198810032978
请求参数: {"name":"\u6234\u5fd7\u8d35","idcard":"320981198810032978"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%9F%A9%E6%99%93%E9%A3%8E&idcard=220822200507170412请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%9F%A9%E6%99%93%E9%A3%8E&idcard=220822200507170412
请求参数: {"name":"\u97e9\u6653\u98ce","idcard":"220822200507170412"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%9F%A9%E6%99%93%E9%A3%8E&idcard=220822200507170412请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%9F%A9%E6%99%93%E9%A3%8E&idcard=220822200507170412
请求参数: {"name":"\u97e9\u6653\u98ce","idcard":"220822200507170412"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%9F%A9%E6%99%93%E9%A3%8E&idcard=220822200507170412请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%9F%A9%E6%99%93%E9%A3%8E&idcard=220822200507170412
请求参数: {"name":"\u97e9\u6653\u98ce","idcard":"220822200507170412"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%9F%A9%E6%99%93%E9%A3%8E&idcard=220822200507170412请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%9F%A9%E6%99%93%E9%A3%8E&idcard=220822200507170412
请求参数: {"name":"\u97e9\u6653\u98ce","idcard":"220822200507170412"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E6%B5%B7%E6%B6%9B&idcard=522123200209152030请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E6%B5%B7%E6%B6%9B&idcard=522123200209152030
请求参数: {"name":"\u6768\u6d77\u6d9b","idcard":"522123200209152030"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%BE%8A%E5%86%A0%E4%B8%AD&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%BE%8A%E5%86%A0%E4%B8%AD&idcard=******************
请求参数: {"name":"\u7f8a\u51a0\u4e2d","idcard":"******************"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E5%AD%90%E5%B8%8C&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E5%AD%90%E5%B8%8C&idcard=******************
请求参数: {"name":"\u9648\u5b50\u5e0c","idcard":"******************"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%AD%99%E7%85%9C%E6%A1%90&idcard=130427201110256747请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%AD%99%E7%85%9C%E6%A1%90&idcard=130427201110256747
请求参数: {"name":"\u5b59\u715c\u6850","idcard":"130427201110256747"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B0%A2%E6%B0%B8%E5%81%A5&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B0%A2%E6%B0%B8%E5%81%A5&idcard=******************
请求参数: {"name":"\u8c22\u6c38\u5065","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B0%A2%E6%B0%B8%E5%81%A5&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B0%A2%E6%B0%B8%E5%81%A5&idcard=******************
请求参数: {"name":"\u8c22\u6c38\u5065","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B5%B5%E5%AE%87%E4%BA%AE&idcard=65402120070427373X请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B5%B5%E5%AE%87%E4%BA%AE&idcard=65402120070427373X
请求参数: {"name":"\u8d75\u5b87\u4eae","idcard":"65402120070427373X"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E6%99%AF%E5%AA%9B&idcard=622923199908280826请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E6%99%AF%E5%AA%9B&idcard=622923199908280826
请求参数: {"name":"\u6768\u666f\u5a9b","idcard":"622923199908280826"}
响应数据: 

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B5%B5%E5%AE%87%E4%BA%AE&idcard=65402120070427373X请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B5%B5%E5%AE%87%E4%BA%AE&idcard=65402120070427373X
请求参数: {"name":"\u8d75\u5b87\u4eae","idcard":"65402120070427373X"}
响应数据: 

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B5%B5%E5%AE%87%E4%BA%AE&idcard=65402120070427373X请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B5%B5%E5%AE%87%E4%BA%AE&idcard=65402120070427373X
请求参数: {"name":"\u8d75\u5b87\u4eae","idcard":"65402120070427373X"}
响应数据: 

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B5%B5%E5%AE%87%E4%BA%AE&idcard=65402120070427373X请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B5%B5%E5%AE%87%E4%BA%AE&idcard=65402120070427373X
请求参数: {"name":"\u8d75\u5b87\u4eae","idcard":"65402120070427373X"}
响应数据: 

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B5%B5%E5%AE%87%E4%BA%AE&idcard=65402120070427373X请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B5%B5%E5%AE%87%E4%BA%AE&idcard=65402120070427373X
请求参数: {"name":"\u8d75\u5b87\u4eae","idcard":"65402120070427373X"}
响应数据: 

http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%B8%81%E9%9B%A8%E9%98%B3&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%B8%81%E9%9B%A8%E9%98%B3&idcard=******************
请求参数: {"name":"\u4e01\u96e8\u9633","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%BD%95%E6%98%A5%E5%8D%8E&idcard=320586198702152438请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%BD%95%E6%98%A5%E5%8D%8E&idcard=320586198702152438
请求参数: {"name":"\u4f55\u6625\u534e","idcard":"320586198702152438"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%BD%95%E6%98%A5%E5%8D%8E&idcard=320582198709120812请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%BD%95%E6%98%A5%E5%8D%8E&idcard=320582198709120812
请求参数: {"name":"\u4f55\u6625\u534e","idcard":"320582198709120812"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%BD%95%E6%98%A5%E5%8D%8E&idcard=320586199111155412请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%BD%95%E6%98%A5%E5%8D%8E&idcard=320586199111155412
请求参数: {"name":"\u4f55\u6625\u534e","idcard":"320586199111155412"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%8F%B2%E4%B9%A6%E7%A6%8F&idcard=342501198409227818请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%8F%B2%E4%B9%A6%E7%A6%8F&idcard=342501198409227818
请求参数: {"name":"\u53f2\u4e66\u798f","idcard":"342501198409227818"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%BA%8E%E5%8D%B0%E5%9D%A6&idcard=130534194503266619请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%BA%8E%E5%8D%B0%E5%9D%A6&idcard=130534194503266619
请求参数: {"name":"\u4e8e\u5370\u5766","idcard":"130534194503266619"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E5%B9%B3&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E5%B9%B3&idcard=******************
请求参数: {"name":"\u6768\u5e73","idcard":"******************"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E6%A2%93%E6%A1%89&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E6%A2%93%E6%A1%89&idcard=******************
请求参数: {"name":"\u5f90\u6893\u6849","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%83%91%E6%9C%8B&idcard=320722200207062311请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%83%91%E6%9C%8B&idcard=320722200207062311
请求参数: {"name":"\u90d1\u670b","idcard":"320722200207062311"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E6%A0%BC%E6%A0%BC&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E6%A0%BC%E6%A0%BC&idcard=******************
请求参数: {"name":"\u674e\u683c\u683c","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E7%8E%89%E5%A8%A5&idcard=132927197207032221请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E7%8E%89%E5%A8%A5&idcard=132927197207032221
请求参数: {"name":"\u9648\u7389\u5a25","idcard":"132927197207032221"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%AD%99%E4%B8%96%E6%9D%B0&idcard=340323200704081714请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%AD%99%E4%B8%96%E6%9D%B0&idcard=340323200704081714
请求参数: {"name":"\u5b59\u4e16\u6770","idcard":"340323200704081714"}
http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%AD%99%E4%B8%96%E6%9D%B0&idcard=340323200704081714请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%AD%99%E4%B8%96%E6%9D%B0&idcard=340323200704081714
请求参数: {"name":"\u5b59\u4e16\u6770","idcard":"340323200704081714"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%AD%99%E4%B8%96%E6%9D%B0&idcard=340323200704081714请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%AD%99%E4%B8%96%E6%9D%B0&idcard=340323200704081714
请求参数: {"name":"\u5b59\u4e16\u6770","idcard":"340323200704081714"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%B8%A5%E8%85%BE%E5%B9%B3&idcard=36050219850410251X请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%B8%A5%E8%85%BE%E5%B9%B3&idcard=36050219850410251X
请求参数: {"name":"\u4e25\u817e\u5e73","idcard":"36050219850410251X"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E6%B5%A9%E5%AE%87&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E6%B5%A9%E5%AE%87&idcard=******************
请求参数: {"name":"\u6768\u6d69\u5b87","idcard":"******************"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%BE%AF%E7%B4%AB%E4%BD%B3&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%BE%AF%E7%B4%AB%E4%BD%B3&idcard=******************
请求参数: {"name":"\u4faf\u7d2b\u4f73","idcard":"******************"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E6%96%B0%E7%BF%A0&idcard=370883198512176283请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E6%96%B0%E7%BF%A0&idcard=370883198512176283
请求参数: {"name":"\u5218\u65b0\u7fe0","idcard":"370883198512176283"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E6%96%B0%E7%BF%A0&idcard=370883198512176283请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E6%96%B0%E7%BF%A0&idcard=370883198512176283
请求参数: {"name":"\u5218\u65b0\u7fe0","idcard":"370883198512176283"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%BB%84%E4%BD%B3%E6%80%A1&idcard=370883201102056240请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%BB%84%E4%BD%B3%E6%80%A1&idcard=370883201102056240
请求参数: {"name":"\u9ec4\u4f73\u6021","idcard":"370883201102056240"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9B%B9%E6%B6%A6%E6%B3%BD&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9B%B9%E6%B6%A6%E6%B3%BD&idcard=******************
请求参数: {"name":"\u66f9\u6da6\u6cfd","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9B%B9%E6%B6%A6%E6%B3%BD&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9B%B9%E6%B6%A6%E6%B3%BD&idcard=******************
请求参数: {"name":"\u66f9\u6da6\u6cfd","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%94%A1%E5%B0%B9%E6%85%A7&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%94%A1%E5%B0%B9%E6%85%A7&idcard=******************
请求参数: {"name":"\u8521\u5c39\u6167","idcard":"******************"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%B8%81%E5%B7%A7%E7%8E%B2&idcard=340122200101254084请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%B8%81%E5%B7%A7%E7%8E%B2&idcard=340122200101254084
请求参数: {"name":"\u4e01\u5de7\u73b2","idcard":"340122200101254084"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%B8%81%E5%B7%A7%E7%8E%B2&idcard=340122200101254084请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%B8%81%E5%B7%A7%E7%8E%B2&idcard=340122200101254084
请求参数: {"name":"\u4e01\u5de7\u73b2","idcard":"340122200101254084"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E4%BC%A0%E6%B3%89&idcard=360782199902024411请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E4%BC%A0%E6%B3%89&idcard=360782199902024411
请求参数: {"name":"\u674e\u4f20\u6cc9","idcard":"360782199902024411"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E5%90%91%E4%B8%9C&idcard=150122198412050614请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E5%90%91%E4%B8%9C&idcard=150122198412050614
请求参数: {"name":"\u5f20\u5411\u4e1c","idcard":"150122198412050614"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%8B%8F%E5%90%9B%E8%B1%AA&idcard=320381201211210375请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%8B%8F%E5%90%9B%E8%B1%AA&idcard=320381201211210375
请求参数: {"name":"\u82cf\u541b\u8c6a","idcard":"320381201211210375"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%8B%8F%E5%90%9B%E8%B1%AA&idcard=320381201211210375请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%8B%8F%E5%90%9B%E8%B1%AA&idcard=320381201211210375
请求参数: {"name":"\u82cf\u541b\u8c6a","idcard":"320381201211210375"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%8B%8F%E5%90%9B%E8%B1%AA&idcard=320381201211210375请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%8B%8F%E5%90%9B%E8%B1%AA&idcard=320381201211210375
请求参数: {"name":"\u82cf\u541b\u8c6a","idcard":"320381201211210375"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%83%A1%E6%9E%97%E7%A5%A5&idcard=330802195903141214请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%83%A1%E6%9E%97%E7%A5%A5&idcard=330802195903141214
请求参数: {"name":"\u80e1\u6797\u7965","idcard":"330802195903141214"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%83%A1%E5%8D%9A%E7%86%A0&idcard=330802201808038313请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%83%A1%E5%8D%9A%E7%86%A0&idcard=330802201808038313
请求参数: {"name":"\u80e1\u535a\u71a0","idcard":"330802201808038313"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%83%A1%E5%8D%9A%E7%86%A0&idcard=330802201008038313请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%83%A1%E5%8D%9A%E7%86%A0&idcard=330802201008038313
请求参数: {"name":"\u80e1\u535a\u71a0","idcard":"330802201008038313"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%83%91%E5%BF%97%E8%BE%BE&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%83%91%E5%BF%97%E8%BE%BE&idcard=******************
请求参数: {"name":"\u90d1\u5fd7\u8fbe","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%A1%BE%E6%99%93%E7%BF%A0&idcard=522601199804103122请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%A1%BE%E6%99%93%E7%BF%A0&idcard=522601199804103122
请求参数: {"name":"\u987e\u6653\u7fe0","idcard":"522601199804103122"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%B8%B9%E6%A2%85&idcard=350123197910054510请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%B8%B9%E6%A2%85&idcard=350123197910054510
请求参数: {"name":"\u4e39\u6885","idcard":"350123197910054510"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E5%93%B2%E7%BF%94&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E5%93%B2%E7%BF%94&idcard=******************
请求参数: {"name":"\u9648\u54f2\u7fd4","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E4%BD%B3%E7%A7%8B&idcard=371311200302183145请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E4%BD%B3%E7%A7%8B&idcard=371311200302183145
请求参数: {"name":"\u674e\u4f73\u79cb","idcard":"371311200302183145"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B5%B5%E9%91%AB&idcard=37132420020217151X请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B5%B5%E9%91%AB&idcard=37132420020217151X
请求参数: {"name":"\u8d75\u946b","idcard":"37132420020217151X"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%B8%81%E5%87%AF&idcard=51052519910929343X请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%B8%81%E5%87%AF&idcard=51052519910929343X
请求参数: {"name":"\u4e01\u51ef","idcard":"51052519910929343X"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%B8%81%E5%87%AF&idcard=510525199102293448请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%B8%81%E5%87%AF&idcard=510525199102293448
请求参数: {"name":"\u4e01\u51ef","idcard":"510525199102293448"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%BB%BB%E9%9B%85%E8%8C%B9&idcard=140427200912020063请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%BB%BB%E9%9B%85%E8%8C%B9&idcard=140427200912020063
请求参数: {"name":"\u4efb\u96c5\u8339","idcard":"140427200912020063"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E4%B8%B9%E5%87%A4&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E4%B8%B9%E5%87%A4&idcard=******************
请求参数: {"name":"\u9648\u4e39\u51e4","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E6%80%9D&idcard=350124197504090037请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E6%80%9D&idcard=350124197504090037
请求参数: {"name":"\u9648\u601d","idcard":"350124197504090037"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E5%86%8D%E8%8A%B3&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E5%86%8D%E8%8A%B3&idcard=******************
请求参数: {"name":"\u6768\u518d\u82b3","idcard":"******************"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E5%86%8D%E5%8F%91&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E5%86%8D%E5%8F%91&idcard=******************
请求参数: {"name":"\u6768\u518d\u53d1","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%A7%9A%E6%9D%B0&idcard=330521199609114611请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%A7%9A%E6%9D%B0&idcard=330521199609114611
请求参数: {"name":"\u59da\u6770","idcard":"330521199609114611"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E4%B8%B9%E5%87%A4&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E4%B8%B9%E5%87%A4&idcard=******************
请求参数: {"name":"\u9648\u4e39\u51e4","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E5%B9%B3&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E5%B9%B3&idcard=******************
请求参数: {"name":"\u6768\u5e73","idcard":"******************"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E4%B8%80&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E4%B8%80&idcard=******************
请求参数: {"name":"\u6768\u4e00","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E4%B8%B9%E5%87%A4&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E4%B8%B9%E5%87%A4&idcard=******************
请求参数: {"name":"\u9648\u4e39\u51e4","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E4%B8%B9%E5%87%A4&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E4%B8%B9%E5%87%A4&idcard=******************
请求参数: {"name":"\u9648\u4e39\u51e4","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E5%B9%B3&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E5%B9%B3&idcard=******************
请求参数: {"name":"\u6768\u5e73","idcard":"******************"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E7%8E%89%E9%9C%9E&idcard=231024196808032027请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E7%8E%89%E9%9C%9E&idcard=231024196808032027
请求参数: {"name":"\u5f20\u7389\u971e","idcard":"231024196808032027"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%9C%E6%89%8D%E6%89%8D&idcard=522729201011230094请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%9C%E6%89%8D%E6%89%8D&idcard=522729201011230094
请求参数: {"name":"\u675c\u624d\u624d","idcard":"522729201011230094"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%9C%E5%A4%A9%E6%89%8D&idcard=522729201011230094请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%9C%E5%A4%A9%E6%89%8D&idcard=522729201011230094
请求参数: {"name":"\u675c\u5929\u624d","idcard":"522729201011230094"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%B2%88%E4%BF%8A%E8%AF%AD&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%B2%88%E4%BF%8A%E8%AF%AD&idcard=******************
请求参数: {"name":"\u6c88\u4fca\u8bed","idcard":"******************"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%BB%BB%E9%9B%85%E8%8C%B9&idcard=140427200912020063请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%BB%BB%E9%9B%85%E8%8C%B9&idcard=140427200912020063
请求参数: {"name":"\u4efb\u96c5\u8339","idcard":"140427200912020063"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%92%99%E4%BA%9A%E4%BA%9A&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%92%99%E4%BA%9A%E4%BA%9A&idcard=******************
请求参数: {"name":"\u8499\u4e9a\u4e9a","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E4%B8%B9%E5%87%A4&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E4%B8%B9%E5%87%A4&idcard=******************
请求参数: {"name":"\u9648\u4e39\u51e4","idcard":"******************"}
http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E4%B8%B9%E5%87%A4&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E4%B8%B9%E5%87%A4&idcard=******************
请求参数: {"name":"\u9648\u4e39\u51e4","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%8E%8B%E5%B2%B3%E9%A3%9E&idcard=650104199009131617请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%8E%8B%E5%B2%B3%E9%A3%9E&idcard=650104199009131617
请求参数: {"name":"\u738b\u5cb3\u98de","idcard":"650104199009131617"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E5%B9%B3&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E5%B9%B3&idcard=******************
请求参数: {"name":"\u6768\u5e73","idcard":"******************"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E5%B9%B3&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E5%B9%B3&idcard=******************
请求参数: {"name":"\u6768\u5e73","idcard":"******************"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E5%90%96&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E5%90%96&idcard=******************
请求参数: {"name":"\u6768\u5416","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%AD%99%E7%BA%A2%E8%89%B3&idcard=342501200110157026请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%AD%99%E7%BA%A2%E8%89%B3&idcard=342501200110157026
请求参数: {"name":"\u5b59\u7ea2\u8273","idcard":"342501200110157026"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E5%B9%B3&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E5%B9%B3&idcard=******************
请求参数: {"name":"\u6768\u5e73","idcard":"******************"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B0%A2%E8%AF%AD%E6%B6%B5&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B0%A2%E8%AF%AD%E6%B6%B5&idcard=******************
请求参数: {"name":"\u8c22\u8bed\u6db5","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%BB%84%E7%81%BF&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%BB%84%E7%81%BF&idcard=******************
请求参数: {"name":"\u9ec4\u707f","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E5%BA%94%E5%85%B4&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E5%BA%94%E5%85%B4&idcard=******************
请求参数: {"name":"\u5f20\u5e94\u5174","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9B%B9%E6%B6%A6%E6%B3%BD&idcard=130528200211191839请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9B%B9%E6%B6%A6%E6%B3%BD&idcard=130528200211191839
请求参数: {"name":"\u66f9\u6da6\u6cfd","idcard":"130528200211191839"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9B%B9%E6%B6%A6%E6%B3%BD&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9B%B9%E6%B6%A6%E6%B3%BD&idcard=******************
请求参数: {"name":"\u66f9\u6da6\u6cfd","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%AE%8B%E6%B4%A5%E9%93%96&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%AE%8B%E6%B4%A5%E9%93%96&idcard=******************
请求参数: {"name":"\u5b8b\u6d25\u94d6","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%B1%A0%E9%B9%8F&idcard=331081198601210014请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%B1%A0%E9%B9%8F&idcard=331081198601210014
请求参数: {"name":"\u6c60\u9e4f","idcard":"331081198601210014"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%9F%A6%E6%80%9D%E8%BF%9C&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%9F%A6%E6%80%9D%E8%BF%9C&idcard=******************
请求参数: {"name":"\u97e6\u601d\u8fdc","idcard":"******************"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%94%A1%E5%B0%B9%E6%85%A7&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%94%A1%E5%B0%B9%E6%85%A7&idcard=******************
请求参数: {"name":"\u8521\u5c39\u6167","idcard":"******************"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%BD%99%E4%B8%9A%E8%8E%B9&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%BD%99%E4%B8%9A%E8%8E%B9&idcard=******************
请求参数: {"name":"\u4f59\u4e1a\u83b9","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E5%BE%B7%E5%87%A4&idcard=522322200605016027请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E5%BE%B7%E5%87%A4&idcard=522322200605016027
请求参数: {"name":"\u9648\u5fb7\u51e4","idcard":"522322200605016027"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E5%BE%B7%E5%87%A4&idcard=522322200605016027请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E5%BE%B7%E5%87%A4&idcard=522322200605016027
请求参数: {"name":"\u9648\u5fb7\u51e4","idcard":"522322200605016027"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%9F%B3%E4%B8%80%E4%BC%B6&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%9F%B3%E4%B8%80%E4%BC%B6&idcard=******************
请求参数: {"name":"\u77f3\u4e00\u4f36","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%91%A8%E6%99%93%E5%AE%81&idcard=320324201010295176请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%91%A8%E6%99%93%E5%AE%81&idcard=320324201010295176
请求参数: {"name":"\u5468\u6653\u5b81","idcard":"320324201010295176"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%93%E9%93%81%E5%86%9B&idcard=41010819770618605X请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%93%E9%93%81%E5%86%9B&idcard=41010819770618605X
请求参数: {"name":"\u5f13\u94c1\u519b","idcard":"41010819770618605X"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%A7%9A%E6%9D%B0&idcard=330521199609114611请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%A7%9A%E6%9D%B0&idcard=330521199609114611
请求参数: {"name":"\u59da\u6770","idcard":"330521199609114611"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E6%80%9D%E6%80%9D&idcard=610431200305290624请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E6%80%9D%E6%80%9D&idcard=610431200305290624
请求参数: {"name":"\u5218\u601d\u601d","idcard":"610431200305290624"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E5%A9%89%E5%A9%B7&idcard=610431200709200349请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E5%A9%89%E5%A9%B7&idcard=610431200709200349
请求参数: {"name":"\u674e\u5a49\u5a77","idcard":"610431200709200349"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%BE%B9%E8%89%BA%E7%92%A0&idcard=610324200807152820请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%BE%B9%E8%89%BA%E7%92%A0&idcard=610324200807152820
请求参数: {"name":"\u8fb9\u827a\u74a0","idcard":"610324200807152820"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%A4%A9%E5%AE%87&idcard=610431200611240318请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%A4%A9%E5%AE%87&idcard=610431200611240318
请求参数: {"name":"\u5218\u5929\u5b87","idcard":"610431200611240318"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%AE%8B%E6%80%9D%E6%88%90&idcard=320321201104190419请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%AE%8B%E6%80%9D%E6%88%90&idcard=320321201104190419
请求参数: {"name":"\u5b8b\u601d\u6210","idcard":"320321201104190419"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%AE%8B%E6%80%9D%E6%88%90&idcard=320321201104190413请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%AE%8B%E6%80%9D%E6%88%90&idcard=320321201104190413
请求参数: {"name":"\u5b8b\u601d\u6210","idcard":"320321201104190413"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E8%AF%97&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E8%AF%97&idcard=******************
请求参数: {"name":"\u674e\u8bd7","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B1%E4%BF%8A%E9%A3%9E&idcard=350322201207186515请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B1%E4%BF%8A%E9%A3%9E&idcard=350322201207186515
请求参数: {"name":"\u90b1\u4fca\u98de","idcard":"350322201207186515"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B1%E4%BF%8A%E9%A3%9E&idcard=350322201207186515请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B1%E4%BF%8A%E9%A3%9E&idcard=350322201207186515
请求参数: {"name":"\u90b1\u4fca\u98de","idcard":"350322201207186515"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%93%E5%AE%87%E8%88%AA&idcard=320322200510131314请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%93%E5%AE%87%E8%88%AA&idcard=320322200510131314
请求参数: {"name":"\u9093\u5b87\u822a","idcard":"320322200510131314"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%8E%8B%E6%80%9D%E9%9C%81&idcard=52038120121126002x请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%8E%8B%E6%80%9D%E9%9C%81&idcard=52038120121126002x
请求参数: {"name":"\u738b\u601d\u9701","idcard":"52038120121126002x"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%8E%8B%E6%80%9D%E9%9C%81&idcard=52038120121126002x请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%8E%8B%E6%80%9D%E9%9C%81&idcard=52038120121126002x
请求参数: {"name":"\u738b\u601d\u9701","idcard":"52038120121126002x"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%8E%8B%E6%80%9D%E9%9C%81&idcard=52038120121126002x请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%8E%8B%E6%80%9D%E9%9C%81&idcard=52038120121126002x
请求参数: {"name":"\u738b\u601d\u9701","idcard":"52038120121126002x"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E5%8D%9A%E5%AE%8F&idcard=350128201109050013请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E5%8D%9A%E5%AE%8F&idcard=350128201109050013
请求参数: {"name":"\u9648\u535a\u5b8f","idcard":"350128201109050013"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E8%9C%9C%E6%A0%BC&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E8%9C%9C%E6%A0%BC&idcard=******************
请求参数: {"name":"\u5218\u871c\u683c","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E8%9C%9C%E6%A0%BC&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E8%9C%9C%E6%A0%BC&idcard=******************
请求参数: {"name":"\u5218\u871c\u683c","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%83%91%E5%BE%B7%E5%86%9B&idcard=231085199402162116请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%83%91%E5%BE%B7%E5%86%9B&idcard=231085199402162116
请求参数: {"name":"\u90d1\u5fb7\u519b","idcard":"231085199402162116"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E7%8E%89&idcard=610722200111090923请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E7%8E%89&idcard=610722200111090923
请求参数: {"name":"\u674e\u7389","idcard":"610722200111090923"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%AD%9F%E9%9D%93%E8%8C%B9&idcard=610722200111090923请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%AD%9F%E9%9D%93%E8%8C%B9&idcard=610722200111090923
请求参数: {"name":"\u5b5f\u9753\u8339","idcard":"610722200111090923"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%BD%86%E5%AE%B6%E8%B1%AA&idcard=520203201006160295请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%BD%86%E5%AE%B6%E8%B1%AA&idcard=520203201006160295
请求参数: {"name":"\u4f46\u5bb6\u8c6a","idcard":"520203201006160295"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%B1%AA%E5%9B%AD%E5%9B%AD&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%B1%AA%E5%9B%AD%E5%9B%AD&idcard=******************
请求参数: {"name":"\u6c6a\u56ed\u56ed","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%B2%91%E5%AF%92%E5%A6%8D&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%B2%91%E5%AF%92%E5%A6%8D&idcard=******************
请求参数: {"name":"\u5c91\u5bd2\u598d","idcard":"******************"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E5%BC%BA&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E5%BC%BA&idcard=******************
请求参数: {"name":"\u5f20\u5f3a","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E5%AE%B9&idcard=510226197509238000请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E5%AE%B9&idcard=510226197509238000
请求参数: {"name":"\u674e\u5bb9","idcard":"510226197509238000"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%B9%E4%B8%B9&idcard=320924199110157160请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%B9%E4%B8%B9&idcard=320924199110157160
请求参数: {"name":"\u5218\u4e39\u4e39","idcard":"320924199110157160"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%8E%8B%E8%BE%9B%E5%A9%B7&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%8E%8B%E8%BE%9B%E5%A9%B7&idcard=******************
请求参数: {"name":"\u738b\u8f9b\u5a77","idcard":"******************"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%94%A1%E5%B0%B9%E6%85%A7&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%94%A1%E5%B0%B9%E6%85%A7&idcard=******************
请求参数: {"name":"\u8521\u5c39\u6167","idcard":"******************"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E9%9D%92%E9%9D%92&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E9%9D%92%E9%9D%92&idcard=******************
请求参数: {"name":"\u9648\u9752\u9752","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233021请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233021
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"141028200410233021"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233022请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233022
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"141028200410233022"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233023请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233023
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"141028200410233023"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233024请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233024
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"141028200410233024"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233025请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233025
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"141028200410233025"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233026请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233026
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"141028200410233026"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233027请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233027
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"141028200410233027"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233028请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233028
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"141028200410233028"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233029请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233029
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"141028200410233029"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=14102820041023302x请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=14102820041023302x
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"14102820041023302x"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=14263020041023302x请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=14263020041023302x
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"14263020041023302x"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=142630200410233021请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=142630200410233021
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"142630200410233021"}
响应数据: {"message":"您的访问过于频繁，请稍作休息后再尝试访问。","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=142630200410233022请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=142630200410233022
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"142630200410233022"}
响应数据: {"message":"您的访问过于频繁，请稍作休息后再尝试访问。","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=142630200410233023请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=142630200410233023
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"142630200410233023"}
响应数据: {"message":"您的访问过于频繁，请稍作休息后再尝试访问。","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=142630200410233024请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=142630200410233024
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"142630200410233024"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=142630200410233025请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=142630200410233025
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"142630200410233025"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=142630200410233026请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=142630200410233026
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"142630200410233026"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=142630200410233027请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=142630200410233027
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"142630200410233027"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=142630200410233028请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=142630200410233028
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"142630200410233028"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=142630200410233029请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=142630200410233029
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"142630200410233029"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=142630200410233029请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=142630200410233029
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"142630200410233029"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=142630200410233027请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=142630200410233027
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"142630200410233027"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233064请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233064
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"141028200410233064"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233065请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233065
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"141028200410233065"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233066请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233066
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"141028200410233066"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233067请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233067
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"141028200410233067"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233068请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233068
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"141028200410233068"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233069请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233069
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"141028200410233069"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=14102820041023306x请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=14102820041023306x
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"14102820041023306x"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=14102820041023306X请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=14102820041023306X
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"14102820041023306X"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233081请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233081
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"141028200410233081"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233082请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233082
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"141028200410233082"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233083请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233083
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"141028200410233083"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233084请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233084
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"141028200410233084"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233085请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233085
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"141028200410233085"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233086请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233086
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"141028200410233086"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233087请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233087
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"141028200410233087"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233087请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233087
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"141028200410233087"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233087请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233087
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"141028200410233087"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233087请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233087
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"141028200410233087"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233088请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233088
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"141028200410233088"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233089请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233089
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"141028200410233089"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=14102820041023308x请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=14102820041023308x
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"14102820041023308x"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=14102820041023308X请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=14102820041023308X
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"14102820041023308X"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233001请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233001
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"141028200410233001"}
响应数据: {"message":"您的访问过于频繁，请稍作休息后再尝试访问。","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233002请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233002
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"141028200410233002"}
响应数据: {"message":"您的访问过于频繁，请稍作休息后再尝试访问。","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233003请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233003
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"141028200410233003"}
响应数据: {"message":"您的访问过于频繁，请稍作休息后再尝试访问。","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233004请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233004
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"141028200410233004"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233005请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233005
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"141028200410233005"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233006请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233006
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"141028200410233006"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233007请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233007
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"141028200410233007"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233008请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233008
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"141028200410233008"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233009请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=141028200410233009
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"141028200410233009"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=14102820041023300x请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=14102820041023300x
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"14102820041023300x"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=14102820041023300X请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=14102820041023300X
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"14102820041023300X"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=14263020041023300X请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=14263020041023300X
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"14263020041023300X"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=14263020041023300x请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=14263020041023300x
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"14263020041023300x"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=142630200410233001请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=142630200410233001
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"142630200410233001"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=142630200410233002请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=142630200410233002
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"142630200410233002"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=142630200410233003请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=142630200410233003
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"142630200410233003"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=142630200410233004请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=142630200410233004
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"142630200410233004"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=142630200410233005请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=142630200410233005
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"142630200410233005"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=142630200410233006请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B9%E6%9D%8E%E6%85%A7&idcard=142630200410233006
请求参数: {"name":"\u90b9\u674e\u6167","idcard":"142630200410233006"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E4%B8%89%E5%8B%87&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E4%B8%89%E5%8B%87&idcard=******************
请求参数: {"name":"\u674e\u4e09\u52c7","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%AD%99%E9%9B%AA%E5%A5%B3&idcard=210303198412082729请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%AD%99%E9%9B%AA%E5%A5%B3&idcard=210303198412082729
请求参数: {"name":"\u5b59\u96ea\u5973","idcard":"210303198412082729"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%BB%84%E5%AE%97%E4%BA%AE&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%BB%84%E5%AE%97%E4%BA%AE&idcard=******************
请求参数: {"name":"\u9ec4\u5b97\u4eae","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%92%8B%E7%92%90&idcard=320481199702101822请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%92%8B%E7%92%90&idcard=320481199702101822
请求参数: {"name":"\u848b\u7490","idcard":"320481199702101822"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%AD%99%E5%AE%88%E6%81%92&idcard=370405200501122537请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%AD%99%E5%AE%88%E6%81%92&idcard=370405200501122537
请求参数: {"name":"\u5b59\u5b88\u6052","idcard":"370405200501122537"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E6%95%AC%E5%8F%8C&idcard=330327199709151580请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E6%95%AC%E5%8F%8C&idcard=330327199709151580
请求参数: {"name":"\u6768\u656c\u53cc","idcard":"330327199709151580"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BB%96%E9%94%AE%E5%A0%83&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BB%96%E9%94%AE%E5%A0%83&idcard=******************
请求参数: {"name":"\u5ed6\u952e\u5803","idcard":"******************"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BB%96%E9%94%AE%E5%A0%83&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BB%96%E9%94%AE%E5%A0%83&idcard=******************
请求参数: {"name":"\u5ed6\u952e\u5803","idcard":"******************"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BB%96%E9%94%AE%E5%A0%83&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BB%96%E9%94%AE%E5%A0%83&idcard=******************
请求参数: {"name":"\u5ed6\u952e\u5803","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BB%96%E9%94%AE%E5%A0%83&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BB%96%E9%94%AE%E5%A0%83&idcard=******************
请求参数: {"name":"\u5ed6\u952e\u5803","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BB%96%E9%94%AE%E5%A0%83&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BB%96%E9%94%AE%E5%A0%83&idcard=******************
请求参数: {"name":"\u5ed6\u952e\u5803","idcard":"******************"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BB%96%E9%94%AE%E5%A0%83&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BB%96%E9%94%AE%E5%A0%83&idcard=******************
请求参数: {"name":"\u5ed6\u952e\u5803","idcard":"******************"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BB%96%E9%94%AE%E5%A0%83&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BB%96%E9%94%AE%E5%A0%83&idcard=******************
请求参数: {"name":"\u5ed6\u952e\u5803","idcard":"******************"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BB%96%E9%94%AE%E5%A0%83&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BB%96%E9%94%AE%E5%A0%83&idcard=******************
请求参数: {"name":"\u5ed6\u952e\u5803","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BB%96%E9%94%AE%E5%A0%83&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BB%96%E9%94%AE%E5%A0%83&idcard=******************
请求参数: {"name":"\u5ed6\u952e\u5803","idcard":"******************"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E9%9B%A8%E7%94%9F&idcard=150121199311125013请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E9%9B%A8%E7%94%9F&idcard=150121199311125013
请求参数: {"name":"\u5f20\u96e8\u751f","idcard":"150121199311125013"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E9%9B%A8%E7%94%9F&idcard=15012119931112501X请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E9%9B%A8%E7%94%9F&idcard=15012119931112501X
请求参数: {"name":"\u5f20\u96e8\u751f","idcard":"15012119931112501X"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BB%96%E9%94%AE%E5%A0%83&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BB%96%E9%94%AE%E5%A0%83&idcard=******************
请求参数: {"name":"\u5ed6\u952e\u5803","idcard":"******************"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BB%96%E9%94%AE%E5%A0%83&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BB%96%E9%94%AE%E5%A0%83&idcard=******************
请求参数: {"name":"\u5ed6\u952e\u5803","idcard":"******************"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%BD%95%E8%BE%B0%E5%AE%87&idcard=331082201404120870请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%BD%95%E8%BE%B0%E5%AE%87&idcard=331082201404120870
请求参数: {"name":"\u4f55\u8fb0\u5b87","idcard":"331082201404120870"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%83%91%E6%83%A0&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%83%91%E6%83%A0&idcard=******************
请求参数: {"name":"\u90d1\u60e0","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E5%AE%88%E6%B4%AA&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E5%AE%88%E6%B4%AA&idcard=******************
请求参数: {"name":"\u5f90\u5b88\u6d2a","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E5%AE%88%E6%B4%AA&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E5%AE%88%E6%B4%AA&idcard=******************
请求参数: {"name":"\u5f90\u5b88\u6d2a","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%8E%8B%E7%A7%80%E8%90%8D&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%8E%8B%E7%A7%80%E8%90%8D&idcard=******************
请求参数: {"name":"\u738b\u79c0\u840d","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%8E%8B%E7%A7%80%E8%90%8D&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%8E%8B%E7%A7%80%E8%90%8D&idcard=******************
请求参数: {"name":"\u738b\u79c0\u840d","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E6%97%B6%E5%80%99&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E6%97%B6%E5%80%99&idcard=******************
请求参数: {"name":"\u5f90\u65f6\u5019","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E5%AE%88%E6%B4%AA&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E5%AE%88%E6%B4%AA&idcard=******************
请求参数: {"name":"\u5f90\u5b88\u6d2a","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E5%AE%88%E6%B4%AA&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E5%AE%88%E6%B4%AA&idcard=******************
请求参数: {"name":"\u5f90\u5b88\u6d2a","idcard":"******************"}
http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E5%AE%88%E6%B4%AA&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E5%AE%88%E6%B4%AA&idcard=******************
请求参数: {"name":"\u5f90\u5b88\u6d2a","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E5%AE%88%E6%B4%AA&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E5%AE%88%E6%B4%AA&idcard=******************
请求参数: {"name":"\u5f90\u5b88\u6d2a","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%8E%8B%E5%BC%80%E6%96%87&idcard=310115201012280278请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%8E%8B%E5%BC%80%E6%96%87&idcard=310115201012280278
请求参数: {"name":"\u738b\u5f00\u6587","idcard":"310115201012280278"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9B%BE%E7%82%9C%E5%A6%8D&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9B%BE%E7%82%9C%E5%A6%8D&idcard=******************
请求参数: {"name":"\u66fe\u709c\u598d","idcard":"******************"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%BA%AA%E5%BD%AC&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%BA%AA%E5%BD%AC&idcard=******************
请求参数: {"name":"\u7eaa\u5f6c","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%BA%AA%E5%BD%AC&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%BA%AA%E5%BD%AC&idcard=******************
请求参数: {"name":"\u7eaa\u5f6c","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%96%B9%E6%96%87%E8%90%8D&idcard=341521200711127281请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%96%B9%E6%96%87%E8%90%8D&idcard=341521200711127281
请求参数: {"name":"\u65b9\u6587\u840d","idcard":"341521200711127281"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E8%B4%BA%E8%8A%9D&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E8%B4%BA%E8%8A%9D&idcard=******************
请求参数: {"name":"\u5218\u8d3a\u829d","idcard":"******************"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E8%B4%BA%E8%8A%9D&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E8%B4%BA%E8%8A%9D&idcard=******************
请求参数: {"name":"\u5218\u8d3a\u829d","idcard":"******************"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E8%B4%BA%E8%8A%9D&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E8%B4%BA%E8%8A%9D&idcard=******************
请求参数: {"name":"\u5218\u8d3a\u829d","idcard":"******************"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E8%B4%BA%E8%8A%9D&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E8%B4%BA%E8%8A%9D&idcard=******************
请求参数: {"name":"\u5218\u8d3a\u829d","idcard":"******************"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E8%B4%BA%E8%8A%9D&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E8%B4%BA%E8%8A%9D&idcard=******************
请求参数: {"name":"\u5218\u8d3a\u829d","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BB%96%E9%94%AE%E5%A0%83&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BB%96%E9%94%AE%E5%A0%83&idcard=******************
请求参数: {"name":"\u5ed6\u952e\u5803","idcard":"******************"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E8%B4%BA%E8%8A%9D&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E8%B4%BA%E8%8A%9D&idcard=******************
请求参数: {"name":"\u5218\u8d3a\u829d","idcard":"******************"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E8%B4%BA%E8%8A%9D&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E8%B4%BA%E8%8A%9D&idcard=******************
请求参数: {"name":"\u5218\u8d3a\u829d","idcard":"******************"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E8%B4%BA%E8%8A%9D&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E8%B4%BA%E8%8A%9D&idcard=******************
请求参数: {"name":"\u5218\u8d3a\u829d","idcard":"******************"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E8%B4%BA%E8%8A%9D&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E8%B4%BA%E8%8A%9D&idcard=******************
请求参数: {"name":"\u5218\u8d3a\u829d","idcard":"******************"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E8%B4%BA%E8%8A%9D&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E8%B4%BA%E8%8A%9D&idcard=******************
请求参数: {"name":"\u5218\u8d3a\u829d","idcard":"******************"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E8%B4%BA%E8%8A%9D&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E8%B4%BA%E8%8A%9D&idcard=******************
请求参数: {"name":"\u5218\u8d3a\u829d","idcard":"******************"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%83%A1%E4%BF%8A%E9%A3%9E&idcard=330881199309091919请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%83%A1%E4%BF%8A%E9%A3%9E&idcard=330881199309091919
请求参数: {"name":"\u80e1\u4fca\u98de","idcard":"330881199309091919"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E4%BF%8A%E5%A8%A5&idcard=520122198607171220请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E4%BF%8A%E5%A8%A5&idcard=520122198607171220
请求参数: {"name":"\u6768\u4fca\u5a25","idcard":"520122198607171220"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%82%85%E5%B9%BF&idcard=500234199702033530请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%82%85%E5%B9%BF&idcard=500234199702033530
请求参数: {"name":"\u5085\u5e7f","idcard":"500234199702033530"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BB%96%E9%94%AE%E5%A0%83&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BB%96%E9%94%AE%E5%A0%83&idcard=******************
请求参数: {"name":"\u5ed6\u952e\u5803","idcard":"******************"}
响应数据: 

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BB%96%E9%94%AE%E5%A0%83&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BB%96%E9%94%AE%E5%A0%83&idcard=******************
请求参数: {"name":"\u5ed6\u952e\u5803","idcard":"******************"}
响应数据: 

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%A7%9A%E7%8F%82&idcard=310109195402255283请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%A7%9A%E7%8F%82&idcard=310109195402255283
请求参数: {"name":"\u59da\u73c2","idcard":"310109195402255283"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E8%81%AA%E8%81%AA&idcard=320683199212180410请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E8%81%AA%E8%81%AA&idcard=320683199212180410
请求参数: {"name":"\u5f90\u806a\u806a","idcard":"320683199212180410"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E8%81%AA%E8%81%AA&idcard=320683199212180410请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E8%81%AA%E8%81%AA&idcard=320683199212180410
请求参数: {"name":"\u5f90\u806a\u806a","idcard":"320683199212180410"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E9%93%AE&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E9%93%AE&idcard=******************
请求参数: {"name":"\u5218\u94ee","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E8%81%AA%E8%81%AA&idcard=320683199212180410请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E8%81%AA%E8%81%AA&idcard=320683199212180410
请求参数: {"name":"\u5f90\u806a\u806a","idcard":"320683199212180410"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E6%B5%B7%E6%B5%AA&idcard=522228200204221034请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E6%B5%B7%E6%B5%AA&idcard=522228200204221034
请求参数: {"name":"\u5f20\u6d77\u6d6a","idcard":"522228200204221034"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E4%BA%91%E9%B9%A4&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E4%BA%91%E9%B9%A4&idcard=******************
请求参数: {"name":"\u674e\u4e91\u9e64","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%92%8B%E5%B9%BF%E6%89%8D&idcard=341126200312204032请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%92%8B%E5%B9%BF%E6%89%8D&idcard=341126200312204032
请求参数: {"name":"\u848b\u5e7f\u624d","idcard":"341126200312204032"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E7%90%86&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E7%90%86&idcard=******************
请求参数: {"name":"\u5f20\u7406","idcard":"******************"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%B8%81%E4%B8%BD&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%B8%81%E4%B8%BD&idcard=******************
请求参数: {"name":"\u4e01\u4e3d","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%B8%81%E4%B8%BD&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%B8%81%E4%B8%BD&idcard=******************
请求参数: {"name":"\u4e01\u4e3d","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%91%A8%E5%AE%87%E8%88%AA&idcard=32020319781005151X请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%91%A8%E5%AE%87%E8%88%AA&idcard=32020319781005151X
请求参数: {"name":"\u5468\u5b87\u822a","idcard":"32020319781005151X"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%A4%8F%E6%99%A8%E9%A6%A8&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%A4%8F%E6%99%A8%E9%A6%A8&idcard=******************
请求参数: {"name":"\u590f\u6668\u99a8","idcard":"******************"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E4%B9%90%E8%AF%97&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E4%B9%90%E8%AF%97&idcard=******************
请求参数: {"name":"\u9648\u4e50\u8bd7","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E8%81%AA%E8%81%AA&idcard=320683199212180410请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E8%81%AA%E8%81%AA&idcard=320683199212180410
请求参数: {"name":"\u5f90\u806a\u806a","idcard":"320683199212180410"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%88%B4%E5%A4%A9%E4%BD%91&idcard=340523201210201566请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%88%B4%E5%A4%A9%E4%BD%91&idcard=340523201210201566
请求参数: {"name":"\u6234\u5929\u4f51","idcard":"340523201210201566"}
响应数据: 

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BB%96%E9%94%AE%E5%A0%83&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BB%96%E9%94%AE%E5%A0%83&idcard=******************
请求参数: {"name":"\u5ed6\u952e\u5803","idcard":"******************"}
响应数据: 

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%88%B4%E5%A4%A9%E4%BD%91&idcard=340121201212281059请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%88%B4%E5%A4%A9%E4%BD%91&idcard=340121201212281059
请求参数: {"name":"\u6234\u5929\u4f51","idcard":"340121201212281059"}
响应数据: 

http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%8E%8B%E7%BA%A2%E4%BA%91&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%8E%8B%E7%BA%A2%E4%BA%91&idcard=******************
请求参数: {"name":"\u738b\u7ea2\u4e91","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%8E%8B%E7%BA%A2%E4%BA%91&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%8E%8B%E7%BA%A2%E4%BA%91&idcard=******************
请求参数: {"name":"\u738b\u7ea2\u4e91","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E5%B9%B3&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E5%B9%B3&idcard=******************
请求参数: {"name":"\u6768\u5e73","idcard":"******************"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%A1%BE%E9%A2%96&idcard=320882200304051421请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%A1%BE%E9%A2%96&idcard=320882200304051421
请求参数: {"name":"\u987e\u9896","idcard":"320882200304051421"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%BD%E7%8F%8D&idcard=360733199310070927请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%BD%E7%8F%8D&idcard=360733199310070927
请求参数: {"name":"\u5218\u4e3d\u73cd","idcard":"360733199310070927"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%BD%E7%8F%8D&idcard=360733199310070927请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%BD%E7%8F%8D&idcard=360733199310070927
请求参数: {"name":"\u5218\u4e3d\u73cd","idcard":"360733199310070927"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%BD%E7%8F%8D&idcard=360733199310070926请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%BD%E7%8F%8D&idcard=360733199310070926
请求参数: {"name":"\u5218\u4e3d\u73cd","idcard":"360733199310070926"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%BD%E8%8F%B2&idcard=360733199310070927请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%BD%E8%8F%B2&idcard=360733199310070927
请求参数: {"name":"\u5218\u4e3d\u83f2","idcard":"360733199310070927"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%BD%E7%8F%8D&idcard=360733199310070927请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%BD%E7%8F%8D&idcard=360733199310070927
请求参数: {"name":"\u5218\u4e3d\u73cd","idcard":"360733199310070927"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E6%80%9D%E5%AE%87&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E6%80%9D%E5%AE%87&idcard=******************
请求参数: {"name":"\u9648\u601d\u5b87","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

