<?php
// 配置文件 - 外部API和限流设置

// 外部API配置
$config = [
    // 外部API开关
    'external_api_enabled' => true, // 设置为 false 可关闭外部API调用
    
    // 外部API URL
    'external_api_url' => 'http://103.216.175.76:6354/query',
    
    // 外部API超时设置（秒）
    'external_api_timeout' => 10,
    'external_api_connect_timeout' => 5,
    
    // Token限流配置
    'rate_limit_seconds' => 60, // 每个Token的查询间隔（秒）
    
    // 调试模式
    'debug_mode' => false, // 设置为 true 可输出调试信息
    
    // 日志配置
    'enable_logging' => true, // 是否启用日志记录
    'log_file' => __DIR__ . '/api_logs/external_api_' . date('Y-m-d') . '.log'
];

// 获取配置值的辅助函数
function getConfig($key, $default = null) {
    global $config;
    return isset($config[$key]) ? $config[$key] : $default;
}

// 记录日志的辅助函数
function logMessage($message, $level = 'INFO') {
    if (!getConfig('enable_logging', false)) {
        return;
    }
    
    $logFile = getConfig('log_file');
    $logDir = dirname($logFile);
    
    // 确保日志目录存在
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[$timestamp] [$level] $message" . PHP_EOL;
    
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
}

// 调试输出函数
function debugLog($message) {
    if (getConfig('debug_mode', false)) {
        error_log("[DEBUG] " . $message);
    }
    logMessage($message, 'DEBUG');
}

return $config;
?>
