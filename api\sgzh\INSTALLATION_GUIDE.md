# 外部API功能安装指南

## 安装步骤

### 1. 文件检查
确保以下文件已正确添加到 `api/sgzh/` 目录：
- `config.php` - 配置文件
- `admin.php` - 管理界面
- `test_external_api.php` - 测试脚本
- `README_External_API.md` - 功能说明
- `INSTALLATION_GUIDE.md` - 本安装指南

### 2. 数据库权限检查
确保数据库用户有以下权限：
- CREATE TABLE（用于创建限流表）
- INSERT, UPDATE, SELECT（用于限流记录）

### 3. 目录权限设置
创建日志目录并设置权限：
```bash
mkdir -p api/sgzh/api_logs
chmod 755 api/sgzh/api_logs
```

### 4. 配置文件设置
编辑 `config.php` 文件，根据需要调整以下设置：
- `external_api_enabled` - 外部API开关
- `external_api_url` - 外部API地址
- `rate_limit_seconds` - 限流间隔时间
- `debug_mode` - 调试模式

### 5. 管理员密码设置
编辑 `admin.php` 文件，修改管理员密码：
```php
$admin_password = 'your_secure_password'; // 替换为安全密码
```

## 功能验证

### 1. 基本功能测试
访问以下URL测试基本功能：
```
http://your-domain/api/sgzh/index.php?msg=13800138000&token=test_token
```

### 2. 限流功能测试
连续访问同一个token的请求，验证限流是否生效。

### 3. 管理界面测试
访问管理界面：
```
http://your-domain/api/sgzh/admin.php
```

### 4. 日志功能测试
检查日志文件是否正常生成：
```
api/sgzh/api_logs/external_api_YYYY-MM-DD.log
```

## 配置选项说明

### 外部API开关控制
- 开启：所有查询优先使用外部API
- 关闭：直接使用本地数据库查询

### 限流机制
- 每个Token在指定时间间隔内只能查询一次
- 超出限制返回HTTP 429状态码
- 限流记录存储在数据库表 `rate_limit` 中

### 数据源优先级
1. 外部API（如果开启且可用）
2. 本地数据库（作为备用）

## 故障排除

### 常见问题

#### 1. 外部API调用失败
**症状**：日志中显示CURL错误或HTTP错误
**解决方案**：
- 检查网络连接
- 验证外部API地址
- 检查防火墙设置

#### 2. 限流表创建失败
**症状**：数据库错误，无法创建rate_limit表
**解决方案**：
- 检查数据库用户权限
- 手动创建表：
```sql
CREATE TABLE rate_limit (
    token VARCHAR(255) PRIMARY KEY,
    last_query_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 3. 日志文件无法写入
**症状**：日志功能不工作
**解决方案**：
- 检查目录权限
- 确保Web服务器有写入权限

#### 4. 配置更新不生效
**症状**：通过管理界面更新配置后不生效
**解决方案**：
- 检查config.php文件权限
- 清除PHP缓存（如果使用了OPcache）

### 调试模式
启用调试模式可以获得更详细的日志信息：
1. 在管理界面启用调试模式
2. 或直接编辑config.php：`'debug_mode' => true`

## 安全建议

### 1. 管理界面安全
- 修改默认管理员密码
- 限制管理界面的IP访问
- 使用HTTPS访问管理界面

### 2. 日志文件安全
- 确保日志文件不能被外部直接访问
- 定期清理旧日志文件
- 考虑日志文件的敏感信息

### 3. 外部API安全
- 验证外部API的SSL证书
- 监控外部API的响应时间和可用性
- 设置合理的超时时间

## 性能优化

### 1. 数据库优化
- 为rate_limit表的token字段创建索引
- 定期清理过期的限流记录

### 2. 缓存优化
- 考虑对外部API响应进行短期缓存
- 使用Redis或Memcached提高性能

### 3. 监控建议
- 监控外部API的响应时间
- 监控限流触发频率
- 监控错误日志

## 版本升级

如需升级功能，请：
1. 备份当前配置文件
2. 备份数据库
3. 更新代码文件
4. 检查配置兼容性
5. 测试所有功能

## 技术支持

如遇到问题，请提供：
1. 错误日志内容
2. 配置文件设置
3. 具体的错误现象
4. 服务器环境信息
