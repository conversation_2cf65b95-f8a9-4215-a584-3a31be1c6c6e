#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
摄像头文件传递脚本
直接传递本地文件到摄像头API
"""

import asyncio
import aiohttp
import base64
import os
import random
import time
import json
from typing import List, Dict, Optional
import argparse
from pathlib import Path


class CameraFileSender:
    def __init__(self, base_url: str = "https://scrpapi.vip/puyg/p.php?id=66"):
        """
        初始化摄像头文件发送器
        
        Args:
            base_url: 摄像头API URL
        """
        self.base_url = base_url
        self.session = None
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    def generate_random_spam_text(self) -> str:
        """生成随机变化的垃圾文本"""
        variations = [
            "毒蝎草拟吗", "草拟吗毒蝎", "毒草拟吗蝎", "毒n草拟吗a", "k草拟吗o蝎",
            "草拟吗毒草拟吗蝎", "毒蝎草拟吗草拟吗", "草拟吗草拟吗毒蝎", "毒草拟吗草拟吗蝎", "草拟吗毒蝎草拟吗",
            "毒蝎我草拟吗", "我草拟吗毒蝎", "毒我草拟吗蝎", "草拟吗我毒蝎",
            "毒蝎草你妈", "草你妈毒蝎", "毒草你妈蝎", "草你妈毒草你妈蝎",
            "毒蝎傻逼", "傻逼毒蝎", "毒傻逼蝎", "毒n傻逼a", "k傻逼o蝎",
            "毒蝎死妈", "死妈毒蝎", "毒死妈蝎", "毒n死妈a", "k死妈o蝎",
            "毒蝎垃圾", "垃圾毒蝎", "毒垃圾蝎", "毒n垃圾a", "k垃圾o蝎",
            "毒蝎狗屎", "狗屎毒蝎", "毒狗屎蝎", "毒n狗屎a", "k狗屎o蝎",
            "毒蝎滚蛋", "滚蛋毒蝎", "毒滚蛋蝎", "毒n滚蛋a", "k滚蛋o蝎",
            "毒蝎去死", "去死毒蝎", "毒去死蝎", "毒n去死a", "k去死o蝎",
            "毒蝎脑残", "脑残毒蝎", "毒脑残蝎", "毒n脑残a", "k脑残o蝎",
            "毒蝎白痴", "白痴毒蝎", "毒白痴蝎", "毒n白痴a", "k白痴o蝎",
            "毒蝎智障", "智障毒蝎", "毒智障蝎", "毒n智障a", "k智障o蝎",
            "毒蝎操你妈", "操你妈毒蝎", "毒操你妈蝎", "操你妈毒操你妈蝎",
            "毒蝎 fuck", "fuck 毒蝎", "毒 fuck 蝎", "fuck 毒 fuck 蝎",
            "毒蝎 shit", "shit 毒蝎", "毒 shit 蝎", "shit 毒 shit 蝎"
        ]
        
        # 在内容前后添加随机数字避免重复
        base_text = random.choice(variations)
        prefix_num = random.randint(0, 100)
        suffix_num = random.randint(0, 100)
        return f"{prefix_num}{base_text}{suffix_num}"
    
    def file_to_base64(self, file_path: str) -> str:
        """将文件转换为base64格式"""
        try:
            with open(file_path, 'rb') as f:
                file_data = f.read()
                
            # 转换为base64
            base64_data = base64.b64encode(file_data).decode('utf-8')
            
            # 添加JPEG头部信息（模拟摄像头拍照）
            if file_path.lower().endswith(('.jpg', '.jpeg')):
                mime_type = 'image/jpeg'
            elif file_path.lower().endswith('.png'):
                mime_type = 'image/png'
            elif file_path.lower().endswith('.gif'):
                mime_type = 'image/gif'
            else:
                mime_type = 'image/jpeg'  # 默认为JPEG
                
            return f"data:{mime_type};base64,{base64_data}"
            
        except Exception as e:
            raise Exception(f"文件转换失败: {str(e)}")
    
    async def send_file_to_camera_api(self, file_path: str) -> str:
        """
        发送文件到摄像头API
        
        Args:
            file_path: 本地文件路径
            
        Returns:
            服务器响应文本
        """
        if not self.session:
            raise RuntimeError("请在异步上下文管理器中使用此方法")
            
        # 将文件转换为base64
        image_data = self.file_to_base64(file_path)
        
        # 构建POST数据（模拟JavaScript的fetch请求）
        post_data = f"image={image_data}"
        
        # 添加随机垃圾数据
        for i in range(10):
            spam_key = f"spam_{i}_{random.randint(1000, 9999)}"
            spam_value = self.generate_random_spam_text()
            post_data += f"&{spam_key}={spam_value}"
        
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        try:
            timeout = aiohttp.ClientTimeout(total=60)
            async with self.session.post(self.base_url, data=post_data, headers=headers, timeout=timeout) as response:
                if not response.ok:
                    raise Exception(f"服务器返回错误状态: {response.status}")
                
                return await response.text()
                
        except Exception as e:
            raise Exception(f"发送文件失败: {str(e)}")
    
    async def batch_send_files(self, file_paths: List[str], delay_seconds: float = 0.05, 
                              max_concurrent: int = 12, repeat_count: int = 1) -> List[Dict]:
        """
        批量发送文件
        
        Args:
            file_paths: 文件路径列表
            delay_seconds: 发送间隔
            max_concurrent: 最大并发数
            repeat_count: 每个文件重复发送次数
            
        Returns:
            发送结果列表
        """
        results = []
        semaphore = asyncio.Semaphore(max_concurrent)
        
        # 创建发送任务列表
        tasks = []
        task_index = 1
        
        for repeat in range(repeat_count):
            for file_path in file_paths:
                tasks.append({
                    'file_path': file_path,
                    'index': task_index,
                    'repeat': repeat + 1
                })
                task_index += 1
        
        async def send_single_file(task_data: Dict) -> Dict:
            async with semaphore:
                try:
                    print(f"发送文件 {task_data['index']}: {task_data['file_path']} (第{task_data['repeat']}次)")
                    
                    response = await self.send_file_to_camera_api(task_data['file_path'])
                    
                    result = {
                        'index': task_data['index'],
                        'file_path': task_data['file_path'],
                        'repeat': task_data['repeat'],
                        'success': True,
                        'response': response[:200] + "..." if len(response) > 200 else response,
                        'timestamp': time.time()
                    }
                    
                    print(f"文件 {task_data['index']} 发送成功")
                    
                except Exception as e:
                    result = {
                        'index': task_data['index'],
                        'file_path': task_data['file_path'],
                        'repeat': task_data['repeat'],
                        'success': False,
                        'error': str(e),
                        'timestamp': time.time()
                    }
                    
                    print(f"文件 {task_data['index']} 发送失败: {str(e)}")
                
                # 添加延迟
                if delay_seconds > 0:
                    await asyncio.sleep(delay_seconds)
                    
                return result
        
        # 执行所有任务
        task_coroutines = [send_single_file(task) for task in tasks]
        results = await asyncio.gather(*task_coroutines, return_exceptions=True)
        
        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append({
                    'index': tasks[i]['index'],
                    'file_path': tasks[i]['file_path'],
                    'repeat': tasks[i]['repeat'],
                    'success': False,
                    'error': str(result),
                    'timestamp': time.time()
                })
            else:
                processed_results.append(result)
        
        return processed_results


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='批量发送文件到摄像头API')
    parser.add_argument('--files', type=str, nargs='+', required=True, help='要发送的文件路径列表')
    parser.add_argument('--repeat', type=int, default=10000, help='每个文件重复发送次数（默认10000）')
    parser.add_argument('--delay', type=float, default=0.05, help='发送间隔秒数（默认0.05，每秒20并发）')
    parser.add_argument('--concurrent', type=int, default=12, help='最大并发数（默认12线程）')
    parser.add_argument('--url', type=str, default="https://scrpapi.vip/puyg/p.php?id=66", help='摄像头API URL')
    parser.add_argument('--output', type=str, help='结果输出文件（JSON格式）')
    
    args = parser.parse_args()
    
    # 验证文件是否存在
    valid_files = []
    for file_path in args.files:
        if os.path.exists(file_path):
            valid_files.append(file_path)
            print(f"找到文件: {file_path}")
        else:
            print(f"文件不存在: {file_path}")
    
    if not valid_files:
        print("没有找到有效的文件！")
        return
    
    total_sends = len(valid_files) * args.repeat
    print(f"开始批量发送文件...")
    print(f"文件数量: {len(valid_files)}")
    print(f"每个文件重复: {args.repeat}次")
    print(f"总发送次数: {total_sends}")
    print(f"延迟: {args.delay}秒")
    print(f"并发数: {args.concurrent}")
    print("-" * 50)
    
    async with CameraFileSender(args.url) as sender:
        # 批量发送
        start_time = time.time()
        results = await sender.batch_send_files(
            file_paths=valid_files,
            delay_seconds=args.delay,
            max_concurrent=args.concurrent,
            repeat_count=args.repeat
        )
        end_time = time.time()
        
        # 统计结果
        success_count = sum(1 for r in results if r['success'])
        failure_count = len(results) - success_count
        
        print("-" * 50)
        print(f"发送完成!")
        print(f"总数: {len(results)}")
        print(f"成功: {success_count}")
        print(f"失败: {failure_count}")
        print(f"耗时: {end_time - start_time:.2f}秒")
        
        # 保存结果到文件
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump({
                    'summary': {
                        'total': len(results),
                        'success': success_count,
                        'failure': failure_count,
                        'duration': end_time - start_time,
                        'files': valid_files,
                        'repeat_count': args.repeat
                    },
                    'results': results
                }, f, ensure_ascii=False, indent=2)
            print(f"结果已保存到: {args.output}")
        
        # 显示失败的记录
        if failure_count > 0:
            print("\n失败记录:")
            for result in results:
                if not result['success']:
                    print(f"  文件 {result['index']}: {result['error']}")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"程序执行出错: {str(e)}")
