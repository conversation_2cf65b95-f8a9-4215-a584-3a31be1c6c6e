import os
import re

log_dir = 'api_logs'
# 搜索包含687945672348的行
target_string = '353482726716'
# 搜索11位手机号的正则表达式
phone_pattern = r'(?<!\d)1[3-9]\d{9}(?!\d)'

phone_numbers_found = set()  # 使用set去重
lines_found = []

for root, dirs, files in os.walk(log_dir):
    for file in files:
        file_path = os.path.join(root, file)
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                for line_num, line in enumerate(f, 1):
                    # 只处理包含687945672348的行
                    if target_string in line:
                        # 在包含目标字符串的行中搜索11位手机号
                        matches = re.findall(phone_pattern, line)
                        if matches:
                            # 记录找到的行信息
                            phone_numbers = ', '.join(matches)
                            lines_found.append(f"{file_path}:{line_num} - 找到手机号: {phone_numbers} - {line.strip()}")
                            # 将手机号添加到集合中去重
                            for phone in matches:
                                phone_numbers_found.add(phone)
        except Exception as e:
            pass  # 忽略无法读取的文件

# 输出满足条件的行到sjh.txt
with open('sjh.txt', 'w', encoding='utf-8') as out:
    for line in lines_found:
        out.write(line + '\n')

# 输出所有去重后的手机号到phone_numbers.txt，每行一个
with open('phone_numbers.txt', 'w', encoding='utf-8') as out:
    for phone in sorted(phone_numbers_found):
        out.write(phone + '\n')
