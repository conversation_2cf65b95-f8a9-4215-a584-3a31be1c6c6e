### 会员 Token 注册 API 文档

#### 基本信息
- **接口路径**: `/admapi/vipregister.php`
- **请求方法**: `GET`
- **响应格式**: `application/json; charset=utf-8`

#### 请求参数
- **lx**（必填，字符串）
  - `1`: 注册 38 天会员
  - `2`: 注册 99999 天会员

#### 功能说明
- 根据 `lx` 生成一个唯一的 12 位纯数字会员 Token，写入 `users` 表（`token, time, vipcode=1, viptime, tokencode=200`），并返回注册时间与到期时间。

#### 返回字段
- **code**（int）: 状态码（`200`/`400`/`500`）
- **status**（string）: `success`/`invalid_param`/`error`
- **reg_time**（string）: 注册时间，`Y-m-d H:i:s`
- **token**（string）: 12 位纯数字唯一 Token
- **expire_time**（string）: 到期时间，`Y-m-d H:i:s`

#### 状态码
- `200`: 成功
- `400`: 参数错误（如 `lx` 非 `1` 或 `2`）
- `500`: 服务器错误（如 Token 生成或入库失败）

#### 请求示例
```bash
# 38 天会员
curl -G "http://yourdomain.com/admapi/vipregister.php" --data-urlencode "lx=1"

# 99999 天会员
curl -G "http://yourdomain.com/admapi/vipregister.php" --data-urlencode "lx=2"
```

#### 成功响应示例
```json
{
  "code": 200,
  "status": "success",
  "reg_time": "2025-08-10 12:00:00",
  "token": "123456789012",
  "expire_time": "2025-09-17 12:00:00"
}
```

#### 错误响应示例
```json
{
  "code": 400,
  "status": "invalid_param",
  "message": "参数 lx 必须为 1 或 2"
}
```

```json
{
  "code": 500,
  "status": "error",
  "message": "注册失败，请稍后再试"
}
```

#### 备注
- `token` 为 12 位纯数字且全局唯一。
- 时间基于服务器当前时间计算。
- 已设置 CORS，可跨域调用。 