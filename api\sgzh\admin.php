<?php
// 管理界面 - 控制外部API开关和查看日志

// 简单的身份验证（实际使用中应该使用更安全的方式）
$admin_password = 'admin123'; // 请修改为更安全的密码

if (isset($_POST['password']) && $_POST['password'] === $admin_password) {
    $_SESSION['admin_logged_in'] = true;
}

session_start();

if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    if (isset($_POST['password'])) {
        $error = '密码错误';
    }
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>管理员登录</title>
        <meta charset="utf-8">
        <style>
            body { font-family: Arial, sans-serif; margin: 50px; }
            .login-form { max-width: 300px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; }
            input[type="password"] { width: 100%; padding: 10px; margin: 10px 0; }
            input[type="submit"] { width: 100%; padding: 10px; background: #007cba; color: white; border: none; }
            .error { color: red; }
        </style>
    </head>
    <body>
        <div class="login-form">
            <h2>管理员登录</h2>
            <?php if (isset($error)) echo "<p class='error'>$error</p>"; ?>
            <form method="post">
                <input type="password" name="password" placeholder="请输入管理员密码" required>
                <input type="submit" value="登录">
            </form>
        </div>
    </body>
    </html>
    <?php
    exit;
}

// 处理配置更新
if (isset($_POST['update_config'])) {
    $configFile = __DIR__ . '/config.php';
    $configContent = file_get_contents($configFile);
    
    // 更新外部API开关
    $newApiEnabled = isset($_POST['external_api_enabled']) ? 'true' : 'false';
    $configContent = preg_replace(
        "/'external_api_enabled'\s*=>\s*(true|false)/",
        "'external_api_enabled' => $newApiEnabled",
        $configContent
    );
    
    // 更新限流时间
    if (isset($_POST['rate_limit_seconds']) && is_numeric($_POST['rate_limit_seconds'])) {
        $newRateLimit = intval($_POST['rate_limit_seconds']);
        $configContent = preg_replace(
            "/'rate_limit_seconds'\s*=>\s*\d+/",
            "'rate_limit_seconds' => $newRateLimit",
            $configContent
        );
    }
    
    // 更新调试模式
    $newDebugMode = isset($_POST['debug_mode']) ? 'true' : 'false';
    $configContent = preg_replace(
        "/'debug_mode'\s*=>\s*(true|false)/",
        "'debug_mode' => $newDebugMode",
        $configContent
    );
    
    file_put_contents($configFile, $configContent);
    $success_message = '配置已更新！';
}

// 读取当前配置
include 'config.php';

// 读取日志文件
$logFile = getConfig('log_file');
$logContent = '';
if (file_exists($logFile)) {
    $logLines = file($logFile);
    $logContent = implode('', array_slice($logLines, -50)); // 显示最后50行
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>外部API管理界面</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1000px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; }
        .form-group { margin: 10px 0; }
        label { display: inline-block; width: 200px; }
        input[type="text"], input[type="number"] { padding: 5px; width: 200px; }
        input[type="checkbox"] { margin-right: 10px; }
        input[type="submit"] { padding: 10px 20px; background: #007cba; color: white; border: none; }
        .log-content { background: #f5f5f5; padding: 10px; height: 300px; overflow-y: scroll; font-family: monospace; font-size: 12px; }
        .success { color: green; font-weight: bold; }
        .status { padding: 10px; margin: 10px 0; }
        .status.enabled { background: #d4edda; color: #155724; }
        .status.disabled { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <h1>外部API管理界面</h1>
        
        <?php if (isset($success_message)): ?>
            <p class="success"><?php echo $success_message; ?></p>
        <?php endif; ?>
        
        <div class="section">
            <h2>当前状态</h2>
            <div class="status <?php echo getConfig('external_api_enabled') ? 'enabled' : 'disabled'; ?>">
                外部API状态: <?php echo getConfig('external_api_enabled') ? '已启用' : '已禁用'; ?>
            </div>
            <p>外部API地址: <?php echo getConfig('external_api_url'); ?></p>
            <p>限流间隔: <?php echo getConfig('rate_limit_seconds'); ?> 秒</p>
            <p>调试模式: <?php echo getConfig('debug_mode') ? '已启用' : '已禁用'; ?></p>
        </div>
        
        <div class="section">
            <h2>配置设置</h2>
            <form method="post">
                <div class="form-group">
                    <label>
                        <input type="checkbox" name="external_api_enabled" <?php echo getConfig('external_api_enabled') ? 'checked' : ''; ?>>
                        启用外部API
                    </label>
                </div>
                
                <div class="form-group">
                    <label>限流间隔（秒）:</label>
                    <input type="number" name="rate_limit_seconds" value="<?php echo getConfig('rate_limit_seconds'); ?>" min="1" max="3600">
                </div>
                
                <div class="form-group">
                    <label>
                        <input type="checkbox" name="debug_mode" <?php echo getConfig('debug_mode') ? 'checked' : ''; ?>>
                        启用调试模式
                    </label>
                </div>
                
                <div class="form-group">
                    <input type="submit" name="update_config" value="更新配置">
                </div>
            </form>
        </div>
        
        <div class="section">
            <h2>最近日志 (最后50行)</h2>
            <div class="log-content">
                <?php echo htmlspecialchars($logContent); ?>
            </div>
            <p><small>日志文件: <?php echo $logFile; ?></small></p>
        </div>
        
        <div class="section">
            <h2>操作</h2>
            <p><a href="?logout=1">退出登录</a></p>
        </div>
    </div>
</body>
</html>

<?php
// 处理退出登录
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: admin.php');
    exit;
}
?>
