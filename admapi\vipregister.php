<?php
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json; charset=utf-8');

require '../db.php';

// 读取参数 lx
$lx = isset($_GET['lx']) ? trim($_GET['lx']) : '';

if ($lx !== '1' && $lx !== '2') {
	echo json_encode([
		'code' => 400,
		'status' => 'invalid_param',
		'message' => '参数 lx 必须为 1 或 2'
	], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
	exit;
}

$days = $lx === '1' ? 38 : 99999;
$vipcode = 1; // 会员标记
$tokencode = 200; // 正常状态
$regTime = date('Y-m-d H:i:s');

// 计算到期时间
try {
	$expire = new DateTime($regTime);
	$expire->add(new DateInterval('P' . $days . 'D'));
	$expireTime = $expire->format('Y-m-d H:i:s');
} catch (Exception $e) {
	echo json_encode([
		'code' => 500,
		'status' => 'error',
		'message' => '到期时间计算失败'
	], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
	exit;
}

// 生成唯一 Token（12位纯数字）
function generateToken($length = 12) {
	$characters = '0123456789';
	$charactersLength = strlen($characters);
	$result = '';
	for ($i = 0; $i < $length; $i++) {
		$idx = random_int(0, $charactersLength - 1);
		$result .= $characters[$idx];
	}
	return $result;
}

$token = '';
try {
	$checkStmt = $mysqli->prepare('SELECT 1 FROM users WHERE token = ? LIMIT 1');
	if (!$checkStmt) {
		throw new Exception('数据库预处理失败');
	}
	// 最多尝试 5 次以确保唯一
	$maxAttempts = 5;
	for ($attempt = 0; $attempt < $maxAttempts; $attempt++) {
		$candidate = generateToken(12);
		$checkStmt->bind_param('s', $candidate);
		$checkStmt->execute();
		$res = $checkStmt->get_result();
		if ($res === false || $res->num_rows === 0) {
			$token = $candidate;
			break;
		}
	}
	$checkStmt->close();
	if ($token === '') {
		throw new Exception('无法生成唯一的 Token');
	}
} catch (Throwable $e) {
	echo json_encode([
		'code' => 500,
		'status' => 'error',
		'message' => 'Token 生成失败'
	], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
	exit;
}

// 写入数据库
try {
	$stmt = $mysqli->prepare('INSERT INTO users (token, time, vipcode, viptime, tokencode) VALUES (?, ?, ?, ?, ?)');
	if (!$stmt) {
		throw new Exception('数据库预处理失败');
	}
	$stmt->bind_param('ssisi', $token, $regTime, $vipcode, $expireTime, $tokencode);
	if (!$stmt->execute()) {
		throw new Exception('数据库写入失败');
	}
	$stmt->close();
} catch (Throwable $e) {
	echo json_encode([
		'code' => 500,
		'status' => 'error',
		'message' => '注册失败，请稍后再试'
	], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
	exit;
}

// 返回结果
$response = [
	'code' => 200,
	'status' => 'success',
	'reg_time' => $regTime,
	'token' => $token,
	'expire_time' => $expireTime
];

echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

$mysqli->close(); 