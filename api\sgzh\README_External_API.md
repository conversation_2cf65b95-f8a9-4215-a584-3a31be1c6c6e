# 外部API集成功能说明

## 功能概述

本次更新为 `api/sgzh/index.php` 添加了以下功能：

1. **外部API开关控制** - 可以控制是否调用外部API
2. **Token限流机制** - 每个Token每30秒只能查询一次
3. **智能数据源切换** - 优先使用外部API，失败时自动切换到本地数据库
4. **完整的日志记录** - 记录所有API调用和错误信息
5. **管理界面** - 方便管理员控制设置

## 新增文件

- `config.php` - 配置文件，包含所有可配置的参数
- `admin.php` - 管理界面，用于控制外部API开关和查看日志
- `test_external_api.php` - 测试脚本，用于验证功能
- `README_External_API.md` - 本说明文件

## 配置说明

### config.php 配置项

```php
$config = [
    'external_api_enabled' => true,  // 外部API开关
    'external_api_url' => 'http://103.216.175.76:6354/query',  // 外部API地址
    'external_api_timeout' => 10,  // API超时时间（秒）
    'external_api_connect_timeout' => 5,  // 连接超时时间（秒）
    'rate_limit_seconds' => 30,  // Token限流间隔（秒）
    'debug_mode' => false,  // 调试模式
    'enable_logging' => true,  // 是否启用日志
];
```

## 工作流程

1. **请求接收** - 接收查询请求（msg和token参数）
2. **限流检查** - 检查Token是否在限流期内
3. **外部API调用**（如果开关开启）：
   - 调用外部API：`http://103.216.175.76:6354/query?keyword=xxxxxxxxx`
   - 检查响应格式是否与当前格式相同
   - 如果外部API的`shuju`字段为空，直接响应"库中无记录"
   - 如果外部API有数据，直接返回外部API的数据
4. **本地数据库查询**（外部API失败或关闭时）
5. **响应返回** - 返回统一格式的JSON响应

## API响应格式

### 成功响应（有数据）
```json
{
    "code": 200,
    "message": "查询成功",
    "shuju": "数据内容",
    "execution_time": "0.1234 秒",
    "data_source": "external_api" // 或 "local_database"
}
```

### 无数据响应
```json
{
    "code": 404,
    "message": "库中无记录。",
    "execution_time": "0.1234 秒",
    "data_source": "external_api" // 或 "local_database"
}
```

### 限流响应
```json
{
    "code": 429,
    "message": "查询过于频繁，请等待 25 秒后再试。"
}
```

## 限流机制

- 每个Token每30秒只能查询一次（可配置）
- 使用数据库表`rate_limit`存储Token的最后查询时间
- 超出限制时返回HTTP 429状态码

## 日志功能

- 自动记录所有API调用、错误和重要操作
- 日志文件位置：`api_logs/external_api_YYYY-MM-DD.log`
- 支持不同级别的日志：INFO、WARN、ERROR、DEBUG

## 管理界面使用

1. 访问 `admin.php`
2. 输入管理员密码（默认：admin123，请修改）
3. 可以控制：
   - 外部API开关
   - 限流时间间隔
   - 调试模式
   - 查看最近日志

## 测试方法

运行测试脚本：
```bash
php test_external_api.php
```

或通过浏览器访问：
```
http://your-domain/api/sgzh/index.php?msg=13800138000&token=your_token
```

## 安全注意事项

1. **修改管理员密码** - 请修改 `admin.php` 中的默认密码
2. **限制管理界面访问** - 建议通过IP白名单或其他方式限制管理界面访问
3. **日志文件权限** - 确保日志文件不能被外部直接访问
4. **外部API安全** - 确保外部API的安全性和可靠性

## 故障排除

### 外部API调用失败
- 检查网络连接
- 检查外部API地址是否正确
- 查看日志文件中的错误信息

### 限流表创建失败
- 检查数据库权限
- 确保数据库连接正常

### 配置更新不生效
- 检查 `config.php` 文件权限
- 确保Web服务器有写入权限

## 版本信息

- 版本：1.0
- 更新日期：2025-08-27
- 兼容性：与原有API完全兼容

## 联系支持

如有问题，请查看日志文件或联系技术支持。
