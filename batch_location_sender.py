#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量位置数据传递脚本
基于提供的JavaScript代码逻辑实现
"""

import asyncio
import aiohttp
import random
import time
import json
from typing import List, Dict, Optional
import argparse
import threading
import multiprocessing
from concurrent.futures import ThreadPoolExecutor
import os


class LocationDataSender:
    def __init__(self, base_url: str = "https://konaapi.asia", secret_key: str = "kona草拟吗", trap_id: str = "草拟吗kona"):
        """
        初始化位置数据发送器
        
        Args:
            base_url: 服务器基础URL
            secret_key: 密钥
            trap_id: 陷阱ID
        """
        self.base_url = base_url.rstrip('/')
        self.secret_key = secret_key
        self.trap_id = trap_id
        self.session = None
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()

    def generate_random_spam_text(self) -> str:
        """生成随机变化的垃圾文本"""
        variations = [
            "kona草拟吗", "草拟吗kona", "ko草拟吗na", "kon草拟吗a", "k草拟吗ona",
            "草拟吗ko草拟吗na", "kona草拟吗草拟吗", "草拟吗草拟吗kona", "ko草拟吗草拟吗na", "草拟吗kona草拟吗",
            "kona我草拟吗", "我草拟吗kona", "ko我草拟吗na", "草拟吗我kona",
            "kona草你妈", "草你妈kona", "ko草你妈na", "草你妈ko草你妈na",
            "kona傻逼", "傻逼kona", "ko傻逼na", "kon傻逼a", "k傻逼ona",
            "傻逼ko傻逼na", "kona傻逼傻逼", "傻逼傻逼kona", "ko傻逼傻逼na", "傻逼kona傻逼",
            "kona死妈", "死妈kona", "ko死妈na", "kon死妈a", "k死妈ona",
            "死妈ko死妈na", "kona死妈死妈", "死妈死妈kona", "ko死妈死妈na", "死妈kona死妈",
            "kona垃圾", "垃圾kona", "ko垃圾na", "kon垃圾a", "k垃圾ona",
            "垃圾ko垃圾na", "kona垃圾垃圾", "垃圾垃圾kona", "ko垃圾垃圾na", "垃圾kona垃圾",
            "kona狗屎", "狗屎kona", "ko狗屎na", "kon狗屎a", "k狗屎ona",
            "狗屎ko狗屎na", "kona狗屎狗屎", "狗屎狗屎kona", "ko狗屎狗屎na", "狗屎kona狗屎",
            "kona滚蛋", "滚蛋kona", "ko滚蛋na", "kon滚蛋a", "k滚蛋ona",
            "滚蛋ko滚蛋na", "kona滚蛋滚蛋", "滚蛋滚蛋kona", "ko滚蛋滚蛋na", "滚蛋kona滚蛋",
            "kona去死", "去死kona", "ko去死na", "kon去死a", "k去死ona",
            "去死ko去死na", "kona去死去死", "去死去死kona", "ko去死去死na", "去死kona去死",
            "kona脑残", "脑残kona", "ko脑残na", "kon脑残a", "k脑残ona",
            "脑残ko脑残na", "kona脑残脑残", "脑残脑残kona", "ko脑残脑残na", "脑残kona脑残",
            "kona白痴", "白痴kona", "ko白痴na", "kon白痴a", "k白痴ona",
            "白痴ko白痴na", "kona白痴白痴", "白痴白痴kona", "ko白痴白痴na", "白痴kona白痴",
            "kona智障", "智障kona", "ko智障na", "kon智障a", "k智障ona",
            "智障ko智障na", "kona智障智障", "智障智障kona", "ko智障智障na", "智障kona智障",
            "kona操你妈", "操你妈kona", "ko操你妈na", "操你妈ko操你妈na",
            "kona fuck", "fuck kona", "ko fuck na", "fuck ko fuck na",
            "kona shit", "shit kona", "ko shit na", "shit ko shit na",
            "kona damn", "damn kona", "ko damn na", "damn ko damn na",
            "kona hell", "hell kona", "ko hell na", "hell ko hell na"
        ]

        # 在内容前后添加随机数字避免重复
        base_text = random.choice(variations)
        prefix_num = random.randint(0, 100)
        suffix_num = random.randint(0, 100)
        return f"{prefix_num}{base_text}{suffix_num}"

    async def send_location_to_server(self, lat: float, lon: float, accuracy: float = 10.0) -> str:
        """
        发送位置数据到服务器

        Args:
            lat: 纬度
            lon: 经度
            accuracy: 精度（默认10.0）

        Returns:
            服务器响应文本
        """
        if not self.session:
            raise RuntimeError("请在异步上下文管理器中使用此方法")

        # 构建表单数据，使用随机变化的垃圾内容
        form_data = aiohttp.FormData()
        form_data.add_field('lat', str(lat))
        form_data.add_field('lon', str(lon))
        form_data.add_field('accuracy', str(accuracy))
        form_data.add_field('key', self.generate_random_spam_text())
        form_data.add_field('id', self.generate_random_spam_text())

    def generate_random_field_name(self) -> str:
        """生成随机的字段名"""
        prefixes = [
            "spam", "junk", "trash", "garbage", "waste", "shit", "crap",
            "bullshit", "nonsense", "rubbish", "debris", "scrap", "litter",
            "kona_shit", "kona_trash", "kona_garbage", "kona_waste", "kona_junk",
            "fuck_kona", "damn_kona", "hell_kona", "shit_kona", "crap_kona",
            "useless", "worthless", "pointless", "meaningless", "stupid",
            "idiotic", "moronic", "retarded", "braindead", "mindless"
        ]
        suffixes = [
            "data", "content", "info", "text", "string", "value", "payload",
            "load", "dump", "blob", "chunk", "block", "segment", "piece",
            "fragment", "part", "section", "portion", "element", "item",
            "entry", "record", "field", "attribute", "property", "parameter"
        ]
        return f"{random.choice(prefixes)}_{random.choice(suffixes)}_{random.randint(1, 9999)}"

    async def send_location_to_server(self, lat: float, lon: float, accuracy: float = 10.0) -> str:
        """
        发送位置数据到服务器，使用随机变化的垃圾内容

        Args:
            lat: 纬度
            lon: 经度
            accuracy: 精度（默认10.0）

        Returns:
            服务器响应文本
        """
        if not self.session:
            raise RuntimeError("请在异步上下文管理器中使用此方法")

        # 构建表单数据，使用随机变化的垃圾内容
        form_data = aiohttp.FormData()
        form_data.add_field('lat', str(lat))
        form_data.add_field('lon', str(lon))
        form_data.add_field('accuracy', str(accuracy))
        form_data.add_field('key', self.generate_random_spam_text())
        form_data.add_field('id', self.generate_random_spam_text())

        # 添加大量随机垃圾字段
        for i in range(20):
            # 使用随机字段名和随机垃圾内容
            field_name = self.generate_random_field_name()
            spam_content = self.generate_random_spam_text()
            form_data.add_field(field_name, spam_content)

            # 添加一些组合内容
            combo_content = f"{self.generate_random_spam_text()}_{self.generate_random_spam_text()}"
            combo_field = self.generate_random_field_name()
            form_data.add_field(combo_field, combo_content)

            # 添加一些重复内容
            repeat_content = self.generate_random_spam_text() * random.randint(2, 5)
            repeat_field = self.generate_random_field_name()
            form_data.add_field(repeat_field, repeat_content)

        url = f"{self.base_url}/process_location.php"

        try:
            timeout = aiohttp.ClientTimeout(total=30)  # 30秒超时
            async with self.session.post(url, data=form_data, timeout=timeout) as response:
                if not response.ok:
                    raise Exception(f"服务器返回错误状态: {response.status}")

                return await response.text()

        except Exception as e:
            raise Exception(f"发送位置数据失败: {str(e)}")
    
    def generate_random_locations(self, count: int, center_lat: float = 39.9042, 
                                center_lon: float = 116.4074, radius_km: float = 10.0) -> List[Dict]:
        """
        生成随机位置数据
        
        Args:
            count: 生成数量
            center_lat: 中心纬度（默认北京）
            center_lon: 中心经度（默认北京）
            radius_km: 半径范围（公里）
            
        Returns:
            位置数据列表
        """
        locations = []
        
        for i in range(count):
            # 在指定半径内生成随机位置
            angle = random.uniform(0, 2 * 3.14159)
            distance = random.uniform(0, radius_km)
            
            # 简单的坐标偏移计算（近似）
            lat_offset = (distance / 111.0) * random.uniform(-1, 1)
            lon_offset = (distance / (111.0 * abs(center_lat / 90.0))) * random.uniform(-1, 1)
            
            lat = center_lat + lat_offset
            lon = center_lon + lon_offset
            accuracy = random.uniform(5.0, 50.0)
            
            locations.append({
                'lat': round(lat, 6),
                'lon': round(lon, 6),
                'accuracy': round(accuracy, 1),
                'index': i + 1
            })
            
        return locations
    
    async def batch_send_locations(self, locations: List[Dict], delay_seconds: float = 1.0,
                                 max_concurrent: int = 5) -> List[Dict]:
        """
        批量发送位置数据
        
        Args:
            locations: 位置数据列表
            delay_seconds: 发送间隔（秒）
            max_concurrent: 最大并发数
            
        Returns:
            发送结果列表
        """
        results = []
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def send_single_location(location_data: Dict) -> Dict:
            async with semaphore:
                try:
                    print(f"发送位置 {location_data['index']}: "
                          f"({location_data['lat']}, {location_data['lon']})")

                    response = await self.send_location_to_server(
                        location_data['lat'],
                        location_data['lon'],
                        location_data['accuracy']
                    )
                    
                    result = {
                        'index': location_data['index'],
                        'lat': location_data['lat'],
                        'lon': location_data['lon'],
                        'accuracy': location_data['accuracy'],
                        'success': True,
                        'response': response,
                        'timestamp': time.time()
                    }
                    
                    print(f"位置 {location_data['index']} 发送成功")
                    
                except Exception as e:
                    result = {
                        'index': location_data['index'],
                        'lat': location_data['lat'],
                        'lon': location_data['lon'],
                        'accuracy': location_data['accuracy'],
                        'success': False,
                        'error': str(e),
                        'timestamp': time.time()
                    }
                    
                    print(f"位置 {location_data['index']} 发送失败: {str(e)}")
                
                # 添加延迟
                if delay_seconds > 0:
                    await asyncio.sleep(delay_seconds)
                    
                return result
        
        # 创建所有任务
        tasks = [send_single_location(location) for location in locations]
        
        # 执行所有任务
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append({
                    'index': locations[i]['index'],
                    'lat': locations[i]['lat'],
                    'lon': locations[i]['lon'],
                    'accuracy': locations[i]['accuracy'],
                    'success': False,
                    'error': str(result),
                    'timestamp': time.time()
                })
            else:
                processed_results.append(result)
        
        return processed_results


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='批量发送位置数据')
    parser.add_argument('--count', type=int, default=10000, help='发送数量（默认10000）')
    parser.add_argument('--delay', type=float, default=0.05, help='发送间隔秒数（默认0.05，每秒20并发）')
    parser.add_argument('--concurrent', type=int, default=12, help='最大并发数（默认12线程）')
    parser.add_argument('--center-lat', type=float, default=39.9042, help='中心纬度（默认北京）')
    parser.add_argument('--center-lon', type=float, default=116.4074, help='中心经度（默认北京）')
    parser.add_argument('--radius', type=float, default=10.0, help='半径范围公里（默认10）')
    parser.add_argument('--key', type=str, default="kona草拟吗", help='密钥')
    parser.add_argument('--id', type=str, default="草拟吗kona", help='陷阱ID')
    parser.add_argument('--url', type=str, default="https://konaapi.asia", help='服务器URL')
    parser.add_argument('--output', type=str, help='结果输出文件（JSON格式）')

    
    args = parser.parse_args()
    
    print(f"开始批量发送位置数据...")
    print(f"数量: {args.count}")
    print(f"延迟: {args.delay}秒")
    print(f"并发数: {args.concurrent}")
    print(f"中心位置: ({args.center_lat}, {args.center_lon})")
    print(f"半径: {args.radius}公里")
    print("-" * 50)
    
    async with LocationDataSender(args.url, args.key, args.id) as sender:
        # 生成随机位置
        locations = sender.generate_random_locations(
            count=args.count,
            center_lat=args.center_lat,
            center_lon=args.center_lon,
            radius_km=args.radius
        )
        
        # 批量发送
        start_time = time.time()
        results = await sender.batch_send_locations(
            locations=locations,
            delay_seconds=args.delay,
            max_concurrent=args.concurrent
        )
        end_time = time.time()
        
        # 统计结果
        success_count = sum(1 for r in results if r['success'])
        failure_count = len(results) - success_count
        
        print("-" * 50)
        print(f"发送完成!")
        print(f"总数: {len(results)}")
        print(f"成功: {success_count}")
        print(f"失败: {failure_count}")
        print(f"耗时: {end_time - start_time:.2f}秒")
        
        # 保存结果到文件
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump({
                    'summary': {
                        'total': len(results),
                        'success': success_count,
                        'failure': failure_count,
                        'duration': end_time - start_time
                    },
                    'results': results
                }, f, ensure_ascii=False, indent=2)
            print(f"结果已保存到: {args.output}")
        
        # 显示失败的记录
        if failure_count > 0:
            print("\n失败记录:")
            for result in results:
                if not result['success']:
                    print(f"  位置 {result['index']}: {result['error']}")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"程序执行出错: {str(e)}")
