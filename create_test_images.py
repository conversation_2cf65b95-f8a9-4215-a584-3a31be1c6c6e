#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建测试图片文件
"""

from PIL import Image, ImageDraw, ImageFont
import random
import os


def create_test_image(filename: str, width: int = 640, height: int = 480):
    """创建一个测试图片"""
    # 创建图片
    img = Image.new('RGB', (width, height), color=(random.randint(0, 255), random.randint(0, 255), random.randint(0, 255)))
    draw = ImageDraw.Draw(img)
    
    # 添加一些随机文本
    spam_texts = [
        f"{random.randint(0, 100)}毒蝎草拟吗{random.randint(0, 100)}",
        f"{random.randint(0, 100)}草拟吗毒蝎{random.randint(0, 100)}",
        f"{random.randint(0, 100)}毒蝎草拟吗毒蝎{random.randint(0, 100)}",
        f"{random.randint(0, 100)}毒蝎傻逼{random.randint(0, 100)}",
        f"{random.randint(0, 100)}毒蝎垃圾{random.randint(0, 100)}"
    ]
    
    # 在图片上绘制文本
    for i, text in enumerate(spam_texts):
        x = random.randint(10, width - 200)
        y = random.randint(10, height - 50) 
        color = (random.randint(0, 255), random.randint(0, 255), random.randint(0, 255))
        try:
            draw.text((x, y), text, fill=color)
        except:
            # 如果字体有问题，使用默认字体
            draw.text((x, y), text, fill=color)
    
    # 添加一些随机形状
    for _ in range(10):
        x1, y1 = random.randint(0, width), random.randint(0, height)
        x2, y2 = random.randint(0, width), random.randint(0, height)
        color = (random.randint(0, 255), random.randint(0, 255), random.randint(0, 255))
        draw.rectangle([x1, y1, x2, y2], outline=color, width=2)
    
    # 保存图片
    img.save(filename, 'JPEG', quality=70)
    print(f"创建测试图片: {filename}")


def main():
    """创建多个测试图片"""
    os.makedirs('test_images', exist_ok=True)
    
    # 创建10个不同的测试图片
    for i in range(10):
        filename = f"test_images/test_image_{i+1}.jpg"
        create_test_image(filename)
    
    print(f"已创建10个测试图片在 test_images/ 目录中")


if __name__ == "__main__":
    main()
