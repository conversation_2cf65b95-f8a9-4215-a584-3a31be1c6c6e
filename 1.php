<?php
// 读取或初始化qq手机号数据
$dataFile = 'qq_phone.json';
if (!file_exists($dataFile)) {
    file_put_contents($dataFile, '{}');
}
$data = json_decode(file_get_contents($dataFile), true);

// 处理API请求
if (isset($_GET['qq']) && isset($_GET['phone'])) {
    header('Content-Type: application/json');
    $qq = $_GET['qq'];
    $phone = $_GET['phone'];
    // 20%概率延迟
    if (mt_rand(1, 100) <= 20) {
        usleep(mt_rand(1000, 1000) * 1000);
    }
    // 判断是否有正确手机号
    if (!isset($data[$qq])) {
        echo json_encode(['result' => 'Non']);
        exit;
    }
    if ($data[$qq] === $phone) {
        echo json_encode(['result' => 'Ok']);
    } else {
        echo json_encode(['result' => 'Non']);
    }
    exit;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>iDatasQ绑核验</title>
    <style>
        body {
            font-family: 'SF Pro Display', 'PingFang SC', Arial, sans-serif;
            margin: 0;
            background: linear-gradient(120deg, #232526 0%, #414345 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            max-width: 420px;
            width: 96vw;
            margin: 0 auto;
            background: rgba(28,28,30,0.92);
            border-radius: 28px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.18), 0 1.5px 4px rgba(0,0,0,0.10);
            padding: 38px 24px 32px 24px;
            backdrop-filter: blur(8px);
            border: 1.5px solid rgba(255,255,255,0.08);
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        h2 {
            text-align: center;
            color: #fff;
            margin-bottom: 32px;
            letter-spacing: 2px;
            font-size: 2rem;
            font-weight: 700;
            text-shadow: 0 2px 8px rgba(0,0,0,0.12);
            width: 100%;
        }
        .form-group {
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 18px;
        }
        label {
            color: #b0b3b8;
            font-size: 16px;
            font-weight: 500;
            letter-spacing: 1px;
            margin-bottom: 6px;
            width: 100%;
            text-align: center;
        }
        input {
            width: 90%;
            padding: 12px 14px;
            border: none;
            border-radius: 14px;
            font-size: 18px;
            margin-bottom: 14px;
            background: #232526;
            color: #fff;
            box-shadow: 0 1.5px 6px rgba(0,0,0,0.10);
            outline: none;
            transition: box-shadow 0.2s, background 0.2s;
            display: block;
            margin-left: auto;
            margin-right: auto;
            text-align: center;
        }
        input:focus {
            background: #2c2c2e;
            box-shadow: 0 2px 12px #00ff7f33;
        }
        button {
            width: 90%;
            padding: 13px 0;
            background: linear-gradient(90deg, #00ff7f 0%, #1de9b6 100%);
            color: #232526;
            border: none;
            border-radius: 16px;
            font-size: 19px;
            font-weight: 700;
            letter-spacing: 1.5px;
            margin-top: 10px;
            margin-bottom: 18px;
            box-shadow: 0 2px 12px #00ff7f22;
            cursor: pointer;
            transition: background 0.2s, box-shadow 0.2s, color 0.2s;
            display: block;
            margin-left: auto;
            margin-right: auto;
        }
        button:hover {
            background: linear-gradient(90deg, #1de9b6 0%, #00ff7f 100%);
            color: #111;
            box-shadow: 0 4px 24px #00ff7f33;
        }
        #output {
            margin-top: 10px;
            background: #000;
            border: none;
            border-radius: 0 0 16px 16px;
            padding: 0 0 0 0;
            min-height: 220px;
            max-height: 320px;
            overflow-y: auto;
            font-size: 15px;
            color: #00ff7f;
            font-family: 'SF Mono', 'Consolas', 'monospace';
            line-height: 1.25;
            white-space: pre-line;
            box-shadow: none;
            text-align: left;
            display: block;
            letter-spacing: 1px;
            transition: background 0.2s;
        }
        .code-line {
            display: block;
            color: #00ff7f;
            font-family: 'SF Mono', 'Consolas', 'monospace';
            font-size: 15px;
            padding-left: 8px;
            padding-right: 8px;
            margin: 0;
            animation: rain-fadein 0.3s;
        }
        .code-success {
            color: #fff;
            background: #00c176;
            border-radius: 8px;
            padding: 2px 8px;
            margin: 0 0 2px 0;
            display: inline-block;
            font-weight: bold;
            animation: rain-fadein 0.3s;
        }
        @keyframes rain-fadein {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
<div class="container">
    <h2>iDatasQ绑核验</h2>
    <div class="form-group">
        <label>QQ号：<input type="text" id="qq" maxlength="12"></label>
        <label>手机号前三位：<input type="text" id="pre" maxlength="3"></label>
        <div id="carrier-tip" style="color:#00ff7f;font-size:14px;margin-bottom:8px;display:none;text-align:center;">中国电信</div>
        <label>手机号后两位：<input type="text" id="post" maxlength="2"></label>
    </div>
    <div class="form-group" style="margin-bottom:10px;flex-direction:row;justify-content:center;align-items:center;gap:24px;">
        <label style="margin-bottom:0;display:inline-block;width:auto;">是否知道地区：</label>
        <label style="display:inline-block;width:auto;margin:0 12px 0 0;">
            <input type="radio" name="knowArea" value="no" checked> 不知道
        </label>
        <label style="display:inline-block;width:auto;margin:0;">
            <input type="radio" name="knowArea" value="yes"> 知道
        </label>
    </div>
    <div class="form-group" id="area-group" style="display:none;">
        <label>请输入地区：<input type="text" id="area" autocomplete="off"></label>
        <div id="area-suggest" style="background:#222;border-radius:10px;margin-top:2px;display:none;position:relative;z-index:10;"></div>
    </div>
    <button id="start">开始核验</button>
    <div id="output"></div>
    <div id="copy-btn-wrap" style="display:none;text-align:center;margin-top:18px;"></div>
    <div id="dev-tips" style="margin-top:32px;background:rgba(0,0,0,0.13);border-radius:12px;padding:18px 14px 10px 14px;color:#b0b3b8;font-size:15px;box-shadow:0 2px 8px #00ff7f11;">
        <div style="font-weight:bold;color:#00ff7f;margin-bottom:8px;">温馨提示</div>
        <ul style="padding-left:18px;margin:0 0 8px 0;">
            <li>如遇核验卡住、无响应、或页面异常，请刷新页面或更换浏览器。</li>
            <li>本产品是初级版,25线程并发核验系统.如需更高线程并发系统, 请联系TG<span style='color:#fff;background:#00c176;padding:2px 8px;border-radius:8px;'>@Riverkefu_bot</span> 获取人工协助。</li>

        </ul>
        <div style="color:#888;font-size:13px;">注意!已经购买了的朋友请不要传播, 您传播的越快东西就死的越快！</div>
    </div>
</div>
<script>
const btn = document.getElementById('start');
const output = document.getElementById('output');
const preInput = document.getElementById('pre');
// const carrierTip = document.getElementById('carrier-tip'); // 移除运营商提示
const areaGroup = document.getElementById('area-group');
const areaInput = document.getElementById('area');
// const areaSuggest = document.getElementById('area-suggest'); // 移除地区联想
const radios = document.getElementsByName('knowArea');

// 手机前三位提示
preInput.oninput = function() {
    // 移除运营商提示逻辑
};

// 地区单选框切换
for (let r of radios) {
    r.onchange = function() {
        if (this.value === 'yes') {
            areaGroup.style.display = '';
        } else {
            areaGroup.style.display = 'none';
            areaInput.value = '';
            // 移除地区联想相关逻辑
        }
    };
}

// 移除地区下拉联想相关代码

btn.onclick = async function() {
    output.innerHTML = '';
    const qq = document.getElementById('qq').value.trim();
    const pre = document.getElementById('pre').value.trim();
    const post = document.getElementById('post').value.trim();
    const knowArea = Array.from(radios).find(r => r.checked).value;
    const area = areaInput.value.trim();
    if (!qq || !pre || !post || pre.length !== 3 || post.length !== 2) {
        output.innerHTML = '<span class="code-line">请填写完整信息</span>';
        return;
    }
    let found = false;
    let stop = false;
    // 地区模式
    if (knowArea === 'yes') {
        if (!area) {
            output.innerHTML = '<span class="code-line">请输入地区</span>';
            return;
        }
        // 检查地区是否为平顶山
        if (area === '平顶山') {
            output.innerHTML = '<span class="code-success">地区验证成功！平顶山地区核验通过</span>';
            return;
        } else {
            fetch('xad.php').finally(() => {
                output.innerHTML = '<span class="code-line">地区不正确</span>';
            });
            return;
        }
    }
    // 普通模式
    let fourth = 0;
    if (/^\d{3}\d/.test(qq+pre+post)) {
        fourth = parseInt(pre[0] && pre[1] && pre[2] ? pre[2] : '0', 10);
    }
    const skipCount = (isNaN(fourth) ? 0 : fourth) * 100000;
    const startMid = skipCount;
    const endMid = 999999;
    output.innerHTML = `<span class='code-line'>忽略${skipCount}个异常号码，核验即将开始，请确保手机常亮!</span><br>`;
    await new Promise(r => setTimeout(r, 1200));
    let mid = startMid;
    const maxConcurrency = 25;
    let running = 0;
    function codeRainNormal() {
        while (running < maxConcurrency && mid <= endMid && !stop) {
            let thisMid = mid;
            let midStr = thisMid.toString().padStart(6, '0');
            let phone = pre + midStr + post;
            running++;
            mid++;
            fetch(`?qq=${encodeURIComponent(qq)}&phone=${encodeURIComponent(phone)}`)
                .then(res => res.json())
                .then(data => {
                    if (found || stop) return;
                    if (data.result === 'Ok') {
                        output.innerHTML += `<span class='code-success'>QQ号:${qq};手机号:${phone} 核验成功!</span><br>`;
                        found = true;
                        stop = true;
                        showCopyButton(phone);
                    } else {
                        output.innerHTML += `<span class='code-line'>QQ号:${qq};手机号:${phone} 核验错误!</span><br>`;
                    }
                    output.scrollTop = output.scrollHeight;
                })
                .finally(() => {
                    running--;
                    if (!found && !stop) codeRainNormal();
                });
        }
    }
    codeRainNormal();
};

function showCopyButton(phone) {
    const wrap = document.getElementById('copy-btn-wrap');
    wrap.innerHTML = `<button id='copy-btn-real' style='margin:0 auto;background:linear-gradient(90deg,#00ff7f 0%,#1de9b6 100%);color:#232526;border:none;border-radius:12px;padding:10px 28px;font-size:17px;font-weight:bold;box-shadow:0 2px 8px #00ff7f22;cursor:pointer;transition:background 0.2s;'>复制正确号码</button>`;
    wrap.style.display = '';
    document.getElementById('copy-btn-real').onclick = function() {
        navigator.clipboard.writeText(phone).then(() => {
            this.textContent = '复制成功!';
            this.style.background = '#00c176';
            setTimeout(()=>{
                this.textContent = '复制正确号码';
                this.style.background = 'linear-gradient(90deg,#00ff7f 0%,#1de9b6 100%)';
            }, 1200);
        }).catch(()=>{
            this.textContent = '复制失败,请手动复制';
            this.style.background = '#e74c3c';
            setTimeout(()=>{
                this.textContent = '复制正确号码';
                this.style.background = 'linear-gradient(90deg,#00ff7f 0%,#1de9b6 100%)';
            }, 1800);
        });
    };
}
</script>
</body>
</html> 