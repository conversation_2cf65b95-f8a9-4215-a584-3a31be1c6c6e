<?php
// 测试外部API功能的脚本

// 模拟测试参数
$testCases = [
    [
        'msg' => '13800138000',
        'token' => 'test_token_123',
        'description' => '测试手机号查询'
    ],
    [
        'msg' => '110101199001011234',
        'token' => 'test_token_456',
        'description' => '测试身份证查询'
    ]
];

echo "=== 外部API功能测试 ===\n\n";

foreach ($testCases as $index => $testCase) {
    echo "测试案例 " . ($index + 1) . ": " . $testCase['description'] . "\n";
    echo "参数: msg=" . $testCase['msg'] . ", token=" . $testCase['token'] . "\n";
    
    // 构建请求URL
    $url = "http://localhost/api/sgzh/index.php?msg=" . urlencode($testCase['msg']) . "&token=" . urlencode($testCase['token']);
    
    echo "请求URL: " . $url . "\n";
    
    // 发送请求
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "CURL错误: " . $error . "\n";
    } else {
        echo "HTTP状态码: " . $httpCode . "\n";
        echo "响应内容: " . $response . "\n";
        
        // 解析JSON响应
        $data = json_decode($response, true);
        if ($data) {
            echo "解析后的响应:\n";
            echo "- 状态码: " . ($data['code'] ?? 'N/A') . "\n";
            echo "- 消息: " . ($data['message'] ?? 'N/A') . "\n";
            echo "- 数据源: " . ($data['data_source'] ?? 'N/A') . "\n";
            echo "- 执行时间: " . ($data['execution_time'] ?? 'N/A') . "\n";
        }
    }
    
    echo "\n" . str_repeat("-", 50) . "\n\n";
    
    // 等待一秒避免限流
    sleep(1);
}

echo "=== 限流测试 ===\n\n";

// 测试限流功能
$rateLimitToken = 'rate_limit_test_token';
$testMsg = '13800138000';

echo "连续发送3个请求测试限流功能...\n\n";

for ($i = 1; $i <= 3; $i++) {
    echo "第 $i 次请求:\n";
    
    $url = "http://localhost/api/sgzh/index.php?msg=" . urlencode($testMsg) . "&token=" . urlencode($rateLimitToken);
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "HTTP状态码: " . $httpCode . "\n";
    
    $data = json_decode($response, true);
    if ($data) {
        echo "状态码: " . ($data['code'] ?? 'N/A') . "\n";
        echo "消息: " . ($data['message'] ?? 'N/A') . "\n";
    }
    
    echo "\n";
    
    // 第一次请求后立即发送第二次，第二次后等待2秒再发送第三次
    if ($i == 1) {
        // 立即发送下一个请求测试限流
        continue;
    } elseif ($i == 2) {
        echo "等待2秒...\n";
        sleep(2);
    }
}

echo "测试完成！\n";
?>
