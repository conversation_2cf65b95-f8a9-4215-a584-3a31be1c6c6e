<?php
// 获取用户名参数
$username = isset($_GET['username']) ? trim($_GET['username']) : '';

// 商户配置
$merchant_secret = '5201314876'; // 您的商户秘钥

// 商品配置
$products = [
    84 => ['name' => '10积分', 'price' => 7.08, 'type' => 'points', 'value' => 10, 'emoji' => '💎', 'desc' => '超值积分包'],
    83 => ['name' => '5积分', 'price' => 4.00, 'type' => 'points', 'value' => 5, 'emoji' => '⭐', 'desc' => '精选积分包'],
    82 => ['name' => '1积分', 'price' => 0.80, 'type' => 'points', 'value' => 1, 'emoji' => '✨', 'desc' => '体验积分包'],
    81 => ['name' => '永久会员', 'price' => 68.99, 'type' => 'vip', 'value' => 99999, 'emoji' => '👑', 'desc' => '至尊特权'],
    80 => ['name' => '半年会员', 'price' => 48.99, 'type' => 'vip', 'value' => 180, 'emoji' => '🚀', 'desc' => '长期优享'],
    79 => ['name' => '月度会员', 'price' => 9.99, 'type' => 'vip', 'value' => 30, 'emoji' => '🎯', 'desc' => '热门推荐'],
    78 => ['name' => '一周会员', 'price' => 6.99, 'type' => 'vip', 'value' => 7, 'emoji' => '⚡', 'desc' => '快速体验']
];

// 处理AJAX请求
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    
    $action = $_POST['action'] ?? '';
    
    if ($action === 'create_order') {
        $product_id = intval($_POST['product_id']);
        $pay_type = $_POST['pay_type'] ?? 'wxpay';
        $customer_contact = $_POST['customer_contact'] ?? $username;
        
        // 调用创建订单API
        $url = "https://cloudshop.qnm6.top/create_order.php?" . http_build_query([
            'customer_contact' => $customer_contact,
            'product_id' => $product_id,
            'pay_type' => $pay_type
        ]);
        
        $response = file_get_contents($url);
        echo $response;
        exit;
    }
    
    if ($action === 'check_payment') {
        $order_id = $_POST['order_id'];
        
        // 检查支付状态
        $url = "https://cloudshop.qnm6.top/check_payment_status.php?" . http_build_query([
            'order_id' => $order_id
        ]);
        
        $response = json_decode(file_get_contents($url), true);
        
        if ($response['status'] === 'success' && $response['data']['order_status'] === 'paid') {
            // 支付成功，进行充值
            $product_id = intval($_POST['product_id']);
            $product = $products[$product_id];
            
            // 生成link参数：当前时间（YmdHi格式）的MD5值
            $current_time = date('YmdHi');
            $link_param = md5($current_time);

            if ($product['type'] === 'points') {
                // 充值积分
                $charge_url = "https://cloudshop.qnm6.top/mika/points.php?" . http_build_query([
                    'username' => $username,
                    'points' => $product['value'],
                    'link' => $link_param
                ]);
            } else {
                // 充值会员
                $charge_url = "https://cloudshop.qnm6.top/mika/vip.php?" . http_build_query([
                    'username' => $username,
                    'days' => $product['value'],
                    'link' => $link_param
                ]);
            }
            
            $charge_response = file_get_contents($charge_url);
            $response['charge_result'] = json_decode($charge_response, true);
        }
        
        echo json_encode($response);
        exit;
    }
}

if (empty($username)) {
    die('<div style="text-align:center;margin-top:50px;color:#ff6b35;">错误：缺少用户名参数</div>');
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品购买 - <?php echo htmlspecialchars($username); ?></title>
    <!-- 使用其他可用的二维码库 -->
    <script src="https://unpkg.com/qrcode-generator@1.4.4/qrcode.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            min-height: 100vh;
            padding: 20px;
            position: relative;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.08)"/><circle cx="10" cy="60" r="0.8" fill="rgba(255,255,255,0.06)"/><circle cx="90" cy="30" r="0.6" fill="rgba(255,255,255,0.04)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
            z-index: -1;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 24px;
            padding: 40px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(20px);
            position: relative;
            overflow: hidden;
        }

        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            position: relative;
        }

        .back-btn {
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 8px 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            color: #74b9ff;
            font-weight: 600;
            font-size: 14px;
            backdrop-filter: blur(10px);
            font-family: inherit;
        }

        .back-btn:hover {
            background: #74b9ff;
            color: white;
            transform: translateY(-50%) translateX(-2px);
            box-shadow: 0 4px 12px rgba(116, 185, 255, 0.3);
        }

        .header h1 {
            color: #2d3436;
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #2d3436, #636e72);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header h1::before {
            content: '🛍️';
            margin-right: 12px;
        }

        .username {
            color: #74b9ff;
            font-size: 18px;
            font-weight: 500;
            padding: 8px 16px;
            background: rgba(116, 185, 255, 0.1);
            border-radius: 20px;
            display: inline-block;
        }

        .username::before {
            content: '👤';
            margin-right: 8px;
        }
        
        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .product-card {
            background: white;
            border-radius: 18px;
            padding: 24px;
            border: 2px solid transparent;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
            position: relative;
            overflow: hidden;
        }

        .product-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #74b9ff, #ff6b35);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .product-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .product-card:hover::before {
            transform: scaleX(1);
        }

        .product-card.selected {
            border-color: #ff6b35;
            background: linear-gradient(135deg, #ff6b35, #ff8c42);
            color: white;
            transform: translateY(-5px) scale(1.05);
        }

        .product-card.selected::before {
            transform: scaleX(1);
            background: rgba(255, 255, 255, 0.3);
        }

        .product-emoji {
            font-size: 32px;
            margin-bottom: 12px;
            display: block;
        }

        .product-name {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 6px;
        }

        .product-desc {
            font-size: 14px;
            opacity: 0.8;
            margin-bottom: 12px;
        }

        .product-price {
            font-size: 26px;
            font-weight: 800;
            color: #ff6b35;
        }

        .product-card.selected .product-price {
            color: white;
        }
        
        .payment-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            z-index: 1500;
            backdrop-filter: blur(5px);
        }

        .payment-modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 20px;
            padding: 30px;
            max-width: 400px;
            width: 90%;
            text-align: center;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
        }

        .payment-title {
            font-size: 24px;
            font-weight: 700;
            color: #2d3436;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        
        .section-title {
            font-size: 22px;
            font-weight: 700;
            color: #2d3436;
            margin-bottom: 20px;
            position: relative;
            padding-left: 40px;
        }

        .section-title::before {
            content: '💳';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            font-size: 24px;
        }
        
        .payment-methods {
            display: flex;
            gap: 15px;
            margin-bottom: 25px;
        }
        
        .payment-method {
            flex: 1;
            padding: 18px;
            border: 2px solid #e9ecef;
            border-radius: 16px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: white;
            position: relative;
            font-weight: 600;
            font-size: 16px;
        }

        .payment-method:hover {
            border-color: #74b9ff;
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(116, 185, 255, 0.2);
        }

        .payment-method.selected {
            border-color: #ff6b35;
            background: linear-gradient(135deg, #ff6b35, #ff8c42);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(255, 107, 53, 0.3);
        }
        
        .btn {
            width: 100%;
            padding: 16px 24px;
            border: none;
            border-radius: 16px;
            font-size: 16px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            margin-bottom: 12px;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
            box-shadow: 0 4px 15px rgba(116, 185, 255, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(116, 185, 255, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #ff6b35, #ff8c42);
            color: white;
            box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
        }

        .btn-secondary:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(255, 107, 53, 0.4);
        }
        
        .order-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            z-index: 2000;
            backdrop-filter: blur(5px);
        }

        .order-modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 20px;
            padding: 30px;
            max-width: 450px;
            width: 90%;
            text-align: center;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
        }

        .order-title {
            font-size: 24px;
            font-weight: 700;
            color: #2d3436;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            position: relative;
        }

        .close-btn {
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            cursor: pointer;
            transition: all 0.3s ease;
            color: #636e72;
            font-weight: 700;
            font-size: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: inherit;
        }

        .close-btn:hover {
            background: #ff6b35;
            border-color: #ff6b35;
            color: white;
            transform: translateY(-50%) scale(1.1);
        }

        .order-info {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 16px;
            padding: 25px;
            margin-bottom: 25px;
            text-align: left;
        }

        .order-info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            padding: 8px 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .order-info-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .order-info-label {
            font-weight: 600;
            color: #636e72;
            font-size: 14px;
        }

        .order-info-value {
            font-weight: 700;
            color: #2d3436;
            font-size: 14px;
        }

        .order-buttons {
            display: flex;
            gap: 12px;
        }

        .order-buttons .btn {
            flex: 1;
            margin-bottom: 0;
        }

        .qr-container {
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 16px;
        }

        .qr-title {
            font-size: 16px;
            font-weight: 600;
            color: #2d3436;
            margin-bottom: 15px;
        }

        .qr-code {
            display: inline-block;
            padding: 15px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .qr-tip {
            font-size: 14px;
            color: #636e72;
            margin-top: 12px;
            line-height: 1.4;
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }
        
        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 15px;
            padding: 30px;
            max-width: 400px;
            width: 90%;
            text-align: center;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #74b9ff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 20px;
            }

            .products-grid {
                grid-template-columns: 1fr;
            }

            .payment-methods {
                flex-direction: column;
            }

            .back-btn {
                position: static;
                transform: none;
                margin-bottom: 20px;
                display: inline-block;
            }

            .header {
                text-align: left;
            }

            .header h1 {
                text-align: center;
                margin-top: 10px;
            }

            .username {
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <button onclick="window.close()" class="back-btn">← 返回</button>
            <h1>商品购买</h1>
            <div class="username">用户：<?php echo htmlspecialchars($username); ?></div>
        </div>
        
        <div class="products-grid">
            <?php foreach ($products as $id => $product): ?>
            <div class="product-card" data-id="<?php echo $id; ?>">
                <div class="product-emoji"><?php echo $product['emoji']; ?></div>
                <div class="product-name"><?php echo htmlspecialchars($product['name']); ?></div>
                <div class="product-desc"><?php echo htmlspecialchars($product['desc']); ?></div>
                <div class="product-price">¥<?php echo number_format($product['price'], 2); ?></div>
            </div>
            <?php endforeach; ?>
        </div>
        

        

    </div>
    
    <!-- 支付方式模态框 -->
    <div class="payment-modal" id="paymentModal">
        <div class="payment-modal-content">
            <div class="payment-title">
                <span>💳</span>
                <span>选择支付方式</span>
            </div>
            <div class="payment-methods">
                <div class="payment-method" data-type="wxpay">
                    <div>💬 微信支付</div>
                </div>
                <div class="payment-method" data-type="alipay">
                    <div>🅰️ 支付宝</div>
                </div>
            </div>
            <button class="btn btn-primary" id="createOrderBtn">🚀 确认订单</button>
        </div>
    </div>

    <!-- 订单模态框 -->
    <div class="order-modal" id="orderModal">
        <div class="order-modal-content">
            <div class="order-title">
                <span>📋</span>
                <span>订单详情</span>
                <button class="close-btn" onclick="closeOrderModal()">×</button>
            </div>
            <div class="order-info" id="orderInfo"></div>
            <div class="qr-container" id="qrContainer">
                <div class="qr-title">📱 扫码支付</div>
                <div class="qr-code" id="qrCode"></div>
                <div class="qr-tip">请使用微信或支付宝扫描上方二维码完成支付</div>
            </div>
            <div class="order-buttons">
                <button class="btn btn-primary" id="checkPaymentBtn">✅ 我已支付</button>
            </div>
        </div>
    </div>

    <!-- 通用模态框 -->
    <div class="modal" id="modal">
        <div class="modal-content">
            <div id="modalContent"></div>
        </div>
    </div>

    <script>
        let selectedProduct = null;
        let selectedPayment = null;
        let currentOrder = null;
        
        // 选择商品
        document.querySelectorAll('.product-card').forEach(card => {
            card.addEventListener('click', function() {
                document.querySelectorAll('.product-card').forEach(c => c.classList.remove('selected'));
                this.classList.add('selected');
                selectedProduct = this.dataset.id;
                document.getElementById('paymentModal').style.display = 'block';
            });
        });
        
        // 选择支付方式
        document.querySelectorAll('.payment-method').forEach(method => {
            method.addEventListener('click', function() {
                document.querySelectorAll('.payment-method').forEach(m => m.classList.remove('selected'));
                this.classList.add('selected');
                selectedPayment = this.dataset.type;
            });
        });
        
        // 创建订单
        document.getElementById('createOrderBtn').addEventListener('click', function() {
            if (!selectedProduct || !selectedPayment) {
                showModal('请选择商品和支付方式');
                return;
            }
            
            this.innerHTML = '<span class="loading"></span> 创建订单中...';
            this.disabled = true;
            
            const formData = new FormData();
            formData.append('action', 'create_order');
            formData.append('product_id', selectedProduct);
            formData.append('pay_type', selectedPayment);
            formData.append('customer_contact', '<?php echo $username; ?>');
            
            fetch('', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    currentOrder = data.data;
                    closePaymentModal();
                    showOrderModal(data.data);
                } else {
                    showModal('❌ 订单创建失败：' + data.message);
                }
            })
            .catch(error => {
                showModal('🌐 网络错误，请重试');
            })
            .finally(() => {
                this.innerHTML = '确认订单';
                this.disabled = false;
            });
        });
        
        // 生成二维码的函数
        function generateQRCode(text, containerId, size = 180) {
            const container = document.getElementById(containerId);
            container.innerHTML = ''; // 清空容器
            
            try {
                // 使用 qrcode-generator 库
                const qr = qrcode(0, 'M');
                qr.addData(text);
                qr.make();
                
                // 创建二维码图片
                const qrImage = qr.createImgTag(4, 8);
                container.innerHTML = qrImage;
                
                // 设置样式
                const img = container.querySelector('img');
                if (img) {
                    img.style.width = size + 'px';
                    img.style.height = size + 'px';
                    img.style.border = '1px solid #ddd';
                    img.style.borderRadius = '8px';
                }
            } catch (error) {
                console.error('二维码生成失败:', error);
                // 降级方案：使用在线API
                const qrUrl = `https://api.qrserver.com/v1/create-qr-code/?size=${size}x${size}&data=${encodeURIComponent(text)}`;
                container.innerHTML = `<img src="${qrUrl}" style="width:${size}px;height:${size}px;border:1px solid #ddd;border-radius:8px;" alt="二维码" />`;
            }
        }
        
        // 检查支付状态
        document.getElementById('checkPaymentBtn').addEventListener('click', function() {
            if (!currentOrder) return;
            
            this.innerHTML = '<span class="loading"></span> 检查中...';
            this.disabled = true;
            
            const formData = new FormData();
            formData.append('action', 'check_payment');
            formData.append('order_id', currentOrder.order_info.order_id);
            formData.append('product_id', selectedProduct);
            
            fetch('', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success' && data.data.order_status === 'paid') {
                    closeOrderModal();
                    showModal('🎉 支付成功！充值已完成', true);
                } else {
                    // 直接在订单模态框中显示未支付提示，不关闭模态框
                    showPaymentStatusInOrder('⏳ 订单未支付，请先完成支付');
                }
            })
            .catch(error => {
                showModal('🔍 检查失败，请重试');
            })
            .finally(() => {
                this.innerHTML = '我已支付';
                this.disabled = false;
            });
        });
        
        function showOrderModal(orderData) {
            const orderInfo = document.getElementById('orderInfo');
            orderInfo.innerHTML = `
                <div class="order-info-item">
                    <span class="order-info-label">📋 订单号</span>
                    <span class="order-info-value">${orderData.order_info.order_id}</span>
                </div>
                <div class="order-info-item">
                    <span class="order-info-label">🛍️ 商品</span>
                    <span class="order-info-value">${orderData.order_info.product_name}</span>
                </div>
                <div class="order-info-item">
                    <span class="order-info-label">💰 金额</span>
                    <span class="order-info-value">¥${orderData.order_info.product_price}</span>
                </div>
                <div class="order-info-item">
                    <span class="order-info-label">💳 支付方式</span>
                    <span class="order-info-value">${orderData.pay_type === 'wxpay' ? '微信支付' : '支付宝'}</span>
                </div>
            `;

            // 生成并显示二维码
            generateQRCode(orderData.payment_url, 'qrCode', 180);

            document.getElementById('orderModal').style.display = 'block';
        }

        function closeOrderModal() {
            document.getElementById('orderModal').style.display = 'none';
        }

        function showPaymentModal() {
            document.getElementById('paymentModal').style.display = 'block';
        }

        function closePaymentModal() {
            document.getElementById('paymentModal').style.display = 'none';
        }

        function showPaymentStatusInOrder(message) {
            // 在订单模态框中显示支付状态提示
            const orderModal = document.getElementById('orderModal');
            let statusDiv = orderModal.querySelector('.payment-status');

            if (!statusDiv) {
                statusDiv = document.createElement('div');
                statusDiv.className = 'payment-status';
                statusDiv.style.cssText = `
                    background: linear-gradient(135deg, #ff6b35, #ff8c42);
                    color: white;
                    padding: 12px 16px;
                    border-radius: 12px;
                    margin-bottom: 20px;
                    font-weight: 600;
                    text-align: center;
                    animation: shake 0.5s ease-in-out;
                `;

                const orderButtons = orderModal.querySelector('.order-buttons');
                orderButtons.parentNode.insertBefore(statusDiv, orderButtons);

                // 添加摇晃动画
                const style = document.createElement('style');
                style.textContent = `
                    @keyframes shake {
                        0%, 100% { transform: translateX(0); }
                        25% { transform: translateX(-5px); }
                        75% { transform: translateX(5px); }
                    }
                `;
                document.head.appendChild(style);
            }

            statusDiv.textContent = message;

            // 3秒后自动隐藏提示
            setTimeout(() => {
                if (statusDiv) {
                    statusDiv.remove();
                }
            }, 3000);
        }
        
        function showModal(message, isSuccess = false, showPayButton = false) {
            const modal = document.getElementById('modal');
            const content = document.getElementById('modalContent');

            let buttons = '<button class="btn btn-primary" onclick="closeModal()">✅ 确定</button>';
            if (showPayButton) {
                buttons = `
                    <button class="btn btn-primary" onclick="closeModal()">✅ 确定</button>
                `;
            }

            content.innerHTML = `
                <div style="margin-bottom: 20px; color: ${isSuccess ? '#00b894' : '#2d3436'}; font-size: 16px; line-height: 1.5;">
                    ${message}
                </div>
                ${buttons}
            `;

            modal.style.display = 'block';
        }
        
        function closeModal() {
            document.getElementById('modal').style.display = 'none';
        }
        
        // 点击模态框外部关闭
        document.getElementById('modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        // 订单模态框不允许点击外部关闭
        // document.getElementById('orderModal').addEventListener('click', function(e) {
        //     if (e.target === this) {
        //         closeOrderModal();
        //     }
        // });

        // 点击支付方式模态框外部关闭
        document.getElementById('paymentModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closePaymentModal();
            }
        });
    </script>
</body>
</html>





